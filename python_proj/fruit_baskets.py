def totalFruit(fruits):
    """
    Find the maximum number of fruits that can be picked with two baskets.
    
    Args:
        fruits: List[int] - Array where fruits[i] is the type of fruit the ith tree produces
        
    Returns:
        int - Maximum number of fruits that can be picked
    """
    if not fruits:
        return 0
    
    # Dictionary to store the count of each fruit type in current window
    basket = {}
    
    left = 0  # Left pointer of the sliding window
    max_picked = 0  # Maximum fruits picked
    
    # Iterate through the array with right pointer
    for right in range(len(fruits)):
        # Add current fruit to basket
        fruit_type = fruits[right]
        basket[fruit_type] = basket.get(fruit_type, 0) + 1
        
        # If we have more than 2 types of fruit, shrink window from left
        while len(basket) > 2:
            left_fruit = fruits[left]
            # Decrement count of the fruit at left pointer (we're removing one instance)
            # We can't simply delete it because there might be multiple instances of this fruit
            basket[left_fruit] -= 1
            
            # If count becomes 0, remove the fruit type from basket completely
            if basket[left_fruit] == 0:
                del basket[left_fruit]
                
            # Move left pointer to shrink window
            left += 1
        
        # Update maximum fruits picked (current window size)
        current_picked = right - left + 1
        max_picked = max(max_picked, current_picked)
    
    return max_picked

# Test cases
test_cases = [
    [1, 2, 1],           # Expected: 3
    [0, 1, 2, 2],        # Expected: 3
    [1, 2, 3, 2, 2],     # Expected: 4
    [3, 3, 3, 1, 2, 1, 1, 2, 3, 3, 4],  # Expected: 5
    [1],                 # Expected: 1
    [1, 1, 1, 1],        # Expected: 4
    [1, 2, 3, 4, 5]      # Expected: 2
]

# Run test cases
for i, fruits in enumerate(test_cases):
    result = totalFruit(fruits)
    print(f"Test case {i+1}: {fruits}")
    print(f"Result: {result}")
    print()