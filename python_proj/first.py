

class Solution:
    def nextPermutation(self, nums) -> None:
        """
        Do not return anything, modify nums in-place instead.
        """
        n = len(nums)
        if(n==1):
            return nums
        if(n==2):
            temp = nums[0]
            nums[0] = nums[1]
            nums[1] = temp
        
        reverse_count = [0]*10
        count = [0]*10
        for i in range(n):
            count[nums[i]] = count[nums[i]] + 1
        # print("count: ", count)
        all_good = False
        for index_from_tail in reversed(range(1, n)):
            reverse_count[nums[index_from_tail]] = reverse_count[nums[index_from_tail]] + 1
            # print("reverse_count: ", reverse_count)
            if(nums[index_from_tail-1]<nums[index_from_tail]):
                reverse_count[nums[index_from_tail-1]] = reverse_count[nums[index_from_tail-1]] + 1
                swap_element = nums[index_from_tail-1]
                

                for remaining_element in range(swap_element + 1, 10):
                    if(reverse_count[remaining_element]!=0):
                        reverse_count[remaining_element] = reverse_count[remaining_element] - 1
                        nums[index_from_tail-1] = remaining_element
                        break
                fill_index = index_from_tail
                for each_element in range(10):
                    if(reverse_count[each_element]!=0):
                        for pp in range(reverse_count[each_element]):
                            nums[fill_index] = each_element
                            fill_index = fill_index+1
                        all_good = True
                # print("nums inside: ", nums)
                return
                
                
        j = 0
        if(not all_good):
            for i in range(10):
                if(count[i]!=0):
                    for p in range(count[i]):
                        nums[j]=i
                        j = j+1
        # print("nums", nums)

            
sol = Solution()
nums = [1,3,2]
sol.nextPermutation(nums)

nums = [3,2,1]
sol.nextPermutation(nums)

nums = [1,1,5]
sol.nextPermutation(nums)

nums = [1,2,3]
sol.nextPermutation(nums)


