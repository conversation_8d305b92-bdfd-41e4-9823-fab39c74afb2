from typing import List
import math

class Solution(object):
    def findPeakElement(self, nums):
        """
        Find a peak element in an array (element strictly greater than neighbors).
        A peak element is guaranteed to exist because:
        - Elements outside array bounds are considered -∞
        - Adjacent elements are guaranteed to be different (nums[i] != nums[i+1])
        
        Time Complexity: O(log n) using binary search
        Space Complexity: O(1)
        
        :type nums: List[int]
        :rtype: int
        """
        if not nums:
            return -1
        
        left, right = 0, len(nums) - 1
        
        while left < right:
            mid = (left + right) // 2
            
            # If mid element is greater than its right neighbor,
            # a peak exists in the left half (including mid)
            if nums[mid] > nums[mid + 1]:
                right = mid
            # Otherwise, a peak exists in the right half
            else:
                left = mid + 1
        
        # When left == right, we've found a peak element
        return left
    def repeatedSubstringPattern(self, s):
        """
        :type s: str
        :rtype: bool
        """
        """
        Check if the string can be constructed by repeating a substring.
        
        Time Complexity: O(n)
        Space Complexity: O(1)
        
        :param s: Input string
        :return: True if the string can be formed by repeating a substring, False otherwise
        """
        n = len(s)
        for i in range(1,n//2 + 1):
            # print("i: ", i)
            subs = s[:i]
            # print(subs)
            modified_str = subs * (n//(i))
            # print(modified_str)
            if(modified_str == s):
                return True
        return False
    def maxProfit(self, prices: List[int]) -> int:
        if(len(prices) == 0):
            return 0
        min_price = prices[0]
        max_profit = 0 
        for price in prices:
            min_price = min(min_price, price)
            max_profit = max(max_profit, price - min_price)
        return max_profit

        
    def productExceptSelf(self, nums: List[int]) -> List[int]:
        """
        Compute the product of all elements in nums except nums[i] for each index i.
        
        Time Complexity: O(n)
        Space Complexity: O(1) (not counting the output array)
        
        :param nums: List of integers
        :return: List of products
        """
        n = len(nums)
        output = [1] * n
        left_product = 1
        for i in range(n):
            output[i] *= left_product
            left_product *= nums[i]
        right_product = 1
        for i in range(n - 1, -1, -1):
            output[i] *= right_product
            right_product *= nums[i]
        return output

    def numIslands(self, grid: List[List[str]]) -> int:
        """
        Count the number of islands in a 2D grid.
        
        Time Complexity: O(m * n)
        Space Complexity: O(1)
        
        :param grid: 2D grid of '1's (land) and '0's (water)
        :return: Number of islands
        """
        def dfs(visited: List[List[int]], grid: List[List[str]],i,j):
            row = len(grid)
            col = len(grid[0])
            if(i<0 or i>=row or j<0 or j>=col):
                return
            visited[i][j] = 1
            # print("i:", i, "j:", j)
            # print("visited:", visited)
            if(i+1<row and visited[i+1][j]==0 and grid[i+1][j]=="1"):
                dfs(visited, grid, i+1, j)
            if(j+1<col and visited[i][j+1]==0 and grid[i][j+1]=="1"):
                dfs(visited, grid, i, j+1)
            if(i-1>=0 and visited[i-1][j]==0 and grid[i-1][j]=="1"):
                dfs(visited, grid, i-1, j)
            if(j-1>=0 and visited[i][j-1]==0 and grid[i][j-1]=="1"):
                dfs(visited, grid, i, j-1)

        if not grid:
            return 0
        row = len(grid)
        col = len(grid[0])
        numIsland = 0
        visited = [[0]*col for _ in range(row)]
        for i in range(row):
            for j in range(col):
                # print("visited main loop:", visited)
                if(visited[i][j]==0 and grid[i][j]=="1"):
                    dfs(visited, grid, i, j)
                    numIsland = numIsland +1
                else:
                    continue
        return numIsland

    def numIslandsdfs(self, grid: List[List[str]]) -> int:
        def dfs(grid, i, j):
            if i < 0 or i >= len(grid) or j < 0 or j >= len(grid[0]) or grid[i][j] != '1':
                return
            grid[i][j] = '0'
            dfs(grid, i + 1, j)
            dfs(grid, i - 1, j)
            dfs(grid, i, j + 1)
            dfs(grid, i, j - 1)

        if not grid:
            return 0

        count = 0
        for i in range(len(grid)):
            for j in range(len(grid[0])):
                if grid[i][j] == '1':
                    dfs(grid, i, j)
                    count += 1
        return count
    def numIslandsbfs(self, grid: List[List[str]]) -> int:
        if not grid:
            return 0

        count = 0
        for i in range(len(grid)):
            for j in range(len(grid[0])):
                if grid[i][j] == '1':
                    self.bfs(grid, i, j)
                    count += 1
        return count

    def bfs(self, grid, i, j):
        queue = [(i, j)]
        grid[i][j] = '0'  # Mark the starting cell as visited
        while queue:
            x, y = queue.pop(0)  # Pop once per iteration, not inside the directions loop
            directions = [(0,1), (0,-1), (-1,0), (1,0)]
            for dx, dy in directions:
                nx, ny = x + dx, y + dy
                if 0 <= nx < len(grid) and 0 <= ny < len(grid[0]) and grid[nx][ny] == '1':
                    grid[nx][ny] = '0'
                    queue.append((nx, ny))
                    print(queue)
                    print("grid: ", grid)
    def shortestPathBinaryMatrix(self, grid: List[List[int]]) -> int:
        if not grid or grid[0][0] == 1:
            return -1

        row, col = len(grid), len(grid[0])
        if grid[row-1][col-1] == 1:
            return -1

        if row == 1 and col == 1:
            return 1

        # BFS approach for shortest path
        from collections import deque
        queue = deque([(0, 0, 1)])  # (x, y, distance)
        visited = set([(0, 0)])
        directions = [(0,1), (0,-1), (-1,0), (1,0), (-1,-1), (-1,1), (1,-1), (1,1)]

        while queue:
            x, y, dist = queue.popleft()

            # Check if we reached the destination
            if x == row-1 and y == col-1:
                return dist

            for dx, dy in directions:
                nx, ny = x + dx, y + dy
                if (0 <= nx < row and 0 <= ny < col and
                    grid[nx][ny] == 0 and (nx, ny) not in visited):
                    visited.add((nx, ny))
                    queue.append((nx, ny, dist + 1))

        return -1  # No path found

    def longestConsecutive(self, nums: List[int]) -> int:
        if not nums:
            return 0
        num_set = set(nums)
        max_length = 0
        for num in num_set:
            if num - 1 not in num_set:
                current_num = num
                current_length = 1
                while current_num + 1 in num_set:
                    current_num += 1
                    current_length += 1
                max_length = max(max_length, current_length)
        return max_length
    def twoSum(self, numbers: List[int], target: int) -> List[int]:
        left, right = 0, len(numbers) - 1
        while left < right:
            current_sum = numbers[left] + numbers[right]
            if current_sum == target:
                return [left + 1, right + 1]
            elif current_sum < target:
                left += 1
            else:
                right -= 1
        return []
    
    def lengthOfLongestSubstring(self, s: str) -> int:
        if not s:
            return 0
        char_set = set()
        left = 0
        max_length = 0
        for right in range(len(s)):
            while s[right] in char_set:
                char_set.remove(s[left])
                left += 1
            char_set.add(s[right])
            max_length = max(max_length, right - left + 1)
        return max_length
    
    def minPathSum(self, grid: List[List[int]]) -> int:
        if not grid:
            return 0
        row, col = len(grid), len(grid[0])
        dp = [[0] * col for _ in range(row)]
        dp[0][0] = grid[0][0]
        for i in range(1, row):
            dp[i][0] = dp[i-1][0] + grid[i][0]
        for j in range(1, col):
            dp[0][j] = dp[0][j-1] + grid[0][j]
        for i in range(1, row):
            for j in range(1, col):
                dp[i][j] = min(dp[i-1][j], dp[i][j-1]) + grid[i][j]
        return dp[row-1][col-1]
    
    def minCostClimbingStairs(self, cost: List[int]) -> int:
        if not cost:
            return 0
        n = len(cost)
        dp = [0] * n
        dp[0], dp[1] = cost[0], cost[1]
        for i in range(2, n):
            dp[i] = min(dp[i-1], dp[i-2]) + cost[i]
        return min(dp[n-1], dp[n-2])
    

    def minCostClimbingStairs2(self, cost: List[int]) -> int:
        if not cost:
            return 0
        n = len(cost)
        if(n==1):
            return cost[0]
        if(n==2):
            return min(cost[0], cost[1])
        dp = [9990000]*(n+1)
        dp[0] = 0
        dp[1] = 0
        for i in range(2, n+1):
            dp[i] = min(dp[i-1]+cost[i-1], dp[i-2]+cost[i-2])
        return dp[n]
    def coinChange(self, coins: List[int], amount: int) -> int:
        if amount == 0:
            return 0
        dp = [9990000]*(amount+1)
        dp[0] = 0
        for i in range(1, amount+1):
            for coin in coins:
                if(i-coin>=0):
                    dp[i] = min(dp[i], dp[i-coin]+1)
        if(dp[amount]==9990000):
            return -1
        return dp[amount]
    def minFallingPathSum(self, matrix: List[List[int]]) -> int:
        if not matrix:
            return 0
        row, col = len(matrix), len(matrix[0])
        dp = [[0]*col for _ in range(row)]
        dp[0] = matrix[0]
        print(dp)
        for i in range(1, row):
            for j in range(col):
                dp[i][j] = min(dp[i-1][j], dp[i-1][j-1] if j-1>=0 else 9990000, dp[i-1][j+1] if j+1<col else 9990000) + matrix[i][j]
            print(dp)
        return min(dp[row-1])
    def mincostTickets(self, days: List[int], costs: List[int]) -> int:
        if not days:
            return 0
        dp = [9990000]*(days[-1]+1)
        dp[0] = 0
        for i in range(1, days[-1]+1):
            if i in days:
                dp[i] = min(dp[i-1]+costs[0], dp[i-7]+costs[1] if i-7>=0 else 9990000, dp[i-30]+costs[2] if i-30>=0 else 9990000)
            else:
                dp[i] = dp[i-1]
        return dp[days[-1]]
    
    def minSteps(self, n: int) -> int:
        """
        Find minimum operations to get exactly n 'A's on screen.

        Key insight: To get n A's optimally, we copy when we have j A's
        and paste (n/j - 1) times. Total operations = dp[j] + n/j
        where dp[j] is operations to get j A's.

        Time Complexity: O(n²)
        Space Complexity: O(n)
        """
        if n == 1:
            return 0

        # dp[i] = minimum operations to get i A's
        dp = [float('inf')] * (n + 1)
        dp[1] = 0  # Start with 1 A, 0 operations needed

        for i in range(2, n + 1):
            # Try all possible divisors j of i
            for j in range(1, i):
                if i % j == 0:
                    # Copy when we have j A's, then paste (i/j - 1) times
                    # Total: 1 copy + (i/j - 1) paste = i/j operations from j A's
                    dp[i] = min(dp[i], dp[j] + i // j)

        return dp[n]
    
    
    def numSquares(self, n: int) -> int:
        if n == 0:
            return 0
        dp = [9990000]*(n+1)
        dp[0] = 0
        for i in range(1, n+1):
            for j in range(1, int(i**0.5)+1):
                dp[i] = min(dp[i], dp[i-j*j]+1)
        return dp[n]
    

    def lastStoneWeightII(self, stones: List[int]) -> int:
        """
        Find the smallest possible weight of the last stone.

        Key insight: This is equivalent to partitioning stones into two groups
        such that their weight difference is minimized. We want to find a subset
        with sum closest to total_sum/2.

        Time Complexity: O(n * sum/2) where n is number of stones
        Space Complexity: O(sum/2)
        """
        total_sum = sum(stones)
        target = total_sum // 2

        # dp[j] = True if we can achieve sum j using some subset of stones
        dp = [False] * (target + 1)
        dp[0] = True  # We can always achieve sum 0 with empty subset

        # For each stone, update what sums we can achieve
        for stone in stones:
            # Iterate backwards to avoid using the same stone multiple times
            for j in range(target, stone - 1, -1):
                dp[j] = dp[j] or dp[j - stone]

        # Find the largest sum <= target that we can achieve
        closest_sum = 0
        for j in range(target, -1, -1):
            if dp[j]:
                closest_sum = j
                break

        # The answer is total_sum - 2 * closest_sum
        # Because if one group has sum 'closest_sum', the other has 'total_sum - closest_sum'
        # The difference is |closest_sum - (total_sum - closest_sum)| = |2*closest_sum - total_sum|
        return total_sum - 2 * closest_sum
    
    def numRollsToTarget(self, n: int, k: int, target: int) -> int:
        """
        Find the number of ways to roll n dice with k faces each to get a target sum.

        Key insight: Use dynamic programming to build up the number of ways to reach
        each sum with a certain number of dice. The state dp[i][j] represents the number
        of ways to get a sum of j using i dice.

        Time Complexity: O(n * k * target)
        Space Complexity: O(n * target)
        """
        MOD = 10**9 + 7
        dp = [[0] * (target + 1) for _ in range(n + 1)]
        dp[0][0] = 1  # Base case: 1 way to get sum 0 with 0 dice

        for i in range(1, n + 1):
            for j in range(1, target + 1):
                for face in range(1, k + 1):
                    if j - face >= 0:
                        dp[i][j] = (dp[i][j] + dp[i - 1][j - face]) % MOD

        return dp[n][target]
    def minimumTotal(self, triangle: List[List[int]]) -> int:
        """
        Find the minimum path sum from top to bottom in a triangle.

        Key insight: Use dynamic programming to build up the minimum path sum
        for each position in the triangle. The state dp[i][j] represents the
        minimum path sum to reach position (i, j).

        Time Complexity: O(n²)
        Space Complexity: O(n²)
        """
        if not triangle:
            return 0

        n = len(triangle)
        dp = [[0] * len(row) for row in triangle]
        dp[0][0] = triangle[0][0]

        for i in range(1, n):
            for j in range(len(triangle[i])):
                if j == 0:
                    dp[i][j] = dp[i - 1][j] + triangle[i][j]
                elif j == len(triangle[i]) - 1:
                    dp[i][j] = dp[i - 1][j - 1] + triangle[i][j]
                else:
                    dp[i][j] = min(dp[i - 1][j - 1], dp[i - 1][j]) + triangle[i][j]

        return min(dp[n - 1])
    
    def findMaxForm(self, strs: List[str], m: int, n: int) -> int:
        """
        Find the maximum number of strings you can form with m 0's and n 1's.

        Key insight: Use dynamic programming to build up the maximum number of
        strings that can be formed with a certain number of 0's and 1's. The
        state dp[i][j] represents the maximum number of strings that can be
        formed with i 0's and j 1's.

        Time Complexity: O(l * m * n) where l is the number of strings
        Space Complexity: O(m * n)
        """
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for s in strs:
            zeros = s.count('0')
            ones = len(s) - zeros
            for i in range(m, zeros - 1, -1):
                for j in range(n, ones - 1, -1):
                    dp[i][j] = max(dp[i][j], dp[i - zeros][j - ones] + 1)

        return dp[m][n]
    
    def maximalSquare(self, matrix: List[List[str]]) -> int:
        """
        Find the area of the largest square containing only 1's.

        Key insight: Use dynamic programming to build up the size of the largest
        square that can be formed ending at each cell. The state dp[i][j] represents
        the side length of the largest square with its bottom-right corner at (i, j).

        Time Complexity: O(m * n)
        Space Complexity: O(m * n)
        """
        if not matrix:
            return 0

        m, n = len(matrix), len(matrix[0])
        dp = [[0] * n for _ in range(m)]
        max_side = 0

        for i in range(m):
            for j in range(n):
                if matrix[i][j] == '1':
                    if i == 0 or j == 0:
                        dp[i][j] = 1
                    else:
                        dp[i][j] = min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]) + 1
                    max_side = max(max_side, dp[i][j])

        return max_side * max_side
    def tilingRectangle(self, n: int, m: int) -> int:
        """
        Find the minimum number of squares that can tile a rectangle of size n x m.

        This problem is NP-hard in general. For cases like 11x13, we need backtracking
        with memoization to find optimal solutions that may require complex patterns.

        Time Complexity: Exponential in worst case, but pruned with memoization
        Space Complexity: O(n * m) for memoization
        """
        # Handle base cases
        if n == m:
            return 1
        if n == 1:
            return m
        if m == 1:
            return n

        # Ensure n <= m for consistency
        if n > m:
            n, m = m, n

        # Memoization cache
        memo = {}

        def backtrack(heights):
            """
            heights: array representing the height of each column
            Returns minimum squares needed to complete the tiling
            """
            # Convert to tuple for hashing
            state = tuple(heights)
            if state in memo:
                return memo[state]

            # Find the leftmost lowest column
            min_height = min(heights)
            min_idx = heights.index(min_height)

            # If all columns are at max height, we're done
            if min_height == n:
                return 0

            result = float('inf')

            # Try placing squares of different sizes starting from min_idx
            max_size = min(n - min_height, m - min_idx)

            for size in range(1, max_size + 1):
                # Check if we can place a square of this size
                can_place = True
                for i in range(min_idx, min_idx + size):
                    if i >= m or heights[i] != min_height:
                        can_place = False
                        break

                if can_place:
                    # Place the square
                    new_heights = heights[:]
                    for i in range(min_idx, min_idx + size):
                        new_heights[i] += size

                    # Recursively solve the remaining problem
                    result = min(result, 1 + backtrack(new_heights))

            memo[state] = result
            return result

        # Start with all columns at height 0
        initial_heights = [0] * m
        return backtrack(initial_heights)
    


# Test cases
if __name__ == "__main__":
    solution = Solution()
    
    # Example 1: [1,2,3,1] -> Output: 2 (element 3 is a peak)
    # print(f"Example 1: {solution.findPeakElement([1,2,3,1])}")
    
    # # Example 2: [1,2,1,3,5,6,4] -> Output: 5 (element 6 is a peak)
    # print(f"Example 2: {solution.findPeakElement([1,2,1,3,5,6,4])}")
    
    # # Additional test cases
    # print(f"Single element: {solution.findPeakElement([1])}")  # Single element is always a peak
    # print(f"Two elements ascending: {solution.findPeakElement([1,2])}")  # Last element is peak
    # print(f"Two elements descending: {solution.findPeakElement([2,1])}")  # First element is peak
    # print(f"Multiple peaks: {solution.findPeakElement([1,3,2,4,1,2,1])}")  # Returns any peak (1 or 3)
    # print(solution.repeatedSubstringPattern("abcabcabcabc"))
    # print(solution.repeatedSubstringPattern("aba"))
    # print(solution.repeatedSubstringPattern("abab"))
    # print(solution.maxProfit([7,1,5,3,6,4]))
    # print(solution.maxProfit([7,6,4,3,1]))
    # print(solution.productExceptSelf([1,2,3,4]))
    # print(solution.numIslands([["0","1","0"],["1","0","1"],["0","1","0"]]))
    # print(solution.numIslandsdfs([["1","1","1","1","0"],["1","1","0","1","0"],["1","1","0","0","0"],["0","0","0","0","0"]]))
    # print(solution.shortestPathBinaryMatrix([[0,1,1,0,0,0],[0,1,0,1,1,0],[0,1,1,0,1,0],[0,0,0,1,1,0],[1,1,1,1,1,0],[1,1,1,1,1,0]]))
    # print(solution.shortestPathBinaryMatrix([[0,1],[1,0]]))
    # print(solution.shortestPathBinaryMatrix([[0,0,0],[1,1,0],[1,1,0]]))
    # print(solution.shortestPathBinaryMatrix([[1,0,0],[1,1,0],[1,1,0]]))
    # print(solution.minCostClimbingStairs([1,100,1,1,1,100,1,1,100,1]))
    # print(solution.minCostClimbingStairs2([1]))
    # print(solution.minCostClimbingStairs([0,1,1,0]))
    # print(solution.coinChange([1,2,5], 11))
    # print(solution.minFallingPathSum([[2,1,3],[6,5,4],[7,8,9]]))
    # Test minSteps function
    # print(f"minSteps(1): {solution.minSteps(1)}")  # Expected: 0
    # print(f"minSteps(3): {solution.minSteps(3)}")  # Expected: 3 (Copy, Paste, Paste)
    # print(f"minSteps(6): {solution.minSteps(6)}")  # Expected: 5 (Copy, Paste, Paste, Copy, Paste)
    # print(f"minSteps(9): {solution.minSteps(9)}")  # Expected: 6 (Copy, Paste, Paste, Copy, Paste, Paste)

    # # Test lastStoneWeightII function
    # print(f"lastStoneWeightII([2,7,4,1,8,1]): {solution.lastStoneWeightII([2,7,4,1,8,1])}")  # Expected: 1
    # print(f"lastStoneWeightII([31,26,33,21,40]): {solution.lastStoneWeightII([31,26,33,21,40])}")  # Expected: 5
    # print(f"lastStoneWeightII([1,2]): {solution.lastStoneWeightII([1,2])}")  # Expected: 1
    # print(solution.maximalSquare([["0","0","0","1"],["1","1","0","1"],["1","1","1","1"],["0","1","1","1"],["0","1","1","1"]]))
    print(solution.tilingRectangle(11, 13))




