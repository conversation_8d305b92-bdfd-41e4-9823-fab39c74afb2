from typing import List


class Solution(object):
    def hey(self, nums):
        print("Hello World!")   
    def totalFruit(self, fruits: List[int]) -> int:
        left = 0
        right = 0
        max_fruits = 0
        basket = {}
        while right < len(fruits):
            basket[fruits[right]] = basket.get(fruits[right], 0) + 1
            while len(basket) > 2:
                basket[fruits[left]] -= 1
                if basket[fruits[left]] == 0:
                    del basket[fruits[left]]
                    left += 1
            max_fruits = max(max_fruits, right - left + 1)
            right += 1
        return max_fruits
        
    def orangesRotting(self, grid: List[List[int]]) -> int:
        row = len(grid)
        col = len(grid[0])
        queue = []
        for i in range(row):
            for j in range(col):
                if grid[i][j] == 2:
                    queue.append((i, j, 0))
        while queue:
            x, y, time = queue.pop(0)
            for dx, dy in [(0,1), (0,-1), (-1,0), (1,0)]:
                nx, ny = x + dx, y + dy
                if 0 <= nx < row and 0 <= ny < col and grid[nx][ny] == 1:
                    grid[nx][ny] = 2
                    queue.append((nx, ny, time + 1))
        for i in range(row):
            for j in range(col):
                if grid[i][j] == 1:
                    return -1
        return time
            
    def generateMatrix(self, n: int) -> List[List[int]]:
        matrix = [[0]*n for _ in range(n)]
        left = 0
        right = n - 1
        top = 0
        bottom = n - 1
        num = 1
        while left <= right and top <= bottom:
            for i in range(left, right + 1):
                matrix[top][i] = num
                num += 1
            top += 1
            for i in range(top, bottom + 1):
                matrix[i][right] = num
                num += 1
            right -= 1
            if top <= bottom:
                for i in range(right, left - 1, -1):
                    matrix[bottom][i] = num
                    num += 1
                bottom -= 1
            if left <= right:
                for i in range(bottom, top - 1, -1):
                    matrix[i][left] = num
                    num += 1
                left += 1
        return matrix

    def solve_eligible_positions(self):
        """
        Solve the eligible positions problem.

        Key insight: A position can be eligible if it can eventually absorb all other elements.
        This happens when an element can become the maximum through the operations.

        Analysis of operations:
        1. If a[i] > a[j]: a[i] becomes a[i] + a[j], a[j] becomes 0
        2. If a[i] < a[j]: a[i] becomes 0, a[j] becomes a[i] + a[j]
        3. If a[i] = a[j]: one becomes a[i] + a[j], other becomes 0

        Pattern: The larger element always absorbs the smaller one.
        """

        def count_eligible_positions(arr):
            """
            Count how many positions can become the final sum holder.

            Key insight: A position is eligible if the element can eventually
            absorb all other elements to become the total sum.

            Analysis: An element at position i is eligible if:
            arr[i] + (sum of all smaller elements) >= max(other elements)

            This means the element can absorb smaller elements to become
            large enough to then absorb the largest remaining element.
            """
            n = len(arr)
            eligible_count = 0

            for i in range(n):
                # Check if element at position i can become the final survivor
                current_val = arr[i]
                other_elements = [arr[j] for j in range(n) if j != i]

                if not other_elements:  # Single element case
                    eligible_count += 1
                    continue

                max_other = max(other_elements)
                sum_smaller = sum(x for x in other_elements if x < current_val)

                # Can this element absorb enough to beat the maximum?
                if current_val + sum_smaller >= max_other:
                    eligible_count += 1

            return eligible_count

        # Read number of test cases
        t = int(input())

        for _ in range(t):
            _ = int(input())  # n is not needed, just read and ignore
            arr = list(map(int, input().split()))

            result = count_eligible_positions(arr)
            print(result)



if __name__ == "__main__":
    solution = Solution()
    # solution.hey([1,2,3])
    # print(solution.orangesRotting([[2,1,1],[1,1,0],[0,1,1]]))
    # print(solution.totalFruit([1,2,1]))

    # Test the eligible positions solution
    # Test cases from the problem
    def test_eligible_positions():
        def count_eligible_positions(arr):
            n = len(arr)
            eligible_count = 0

            for i in range(n):
                current_val = arr[i]
                other_elements = [arr[j] for j in range(n) if j != i]

                if not other_elements:
                    eligible_count += 1
                    continue

                max_other = max(other_elements)
                sum_smaller = sum(x for x in other_elements if x < current_val)

                if current_val + sum_smaller >= max_other:
                    eligible_count += 1

            return eligible_count

        # Test cases
        test_cases = [
            ([1, 9, 8], 2),
            ([1, 2, 3, 4, 10000], 1),
            ([2, 2, 2, 2], 4)
        ]

        for i, (arr, expected) in enumerate(test_cases):
            result = count_eligible_positions(arr)
            print(f"Test {i+1}: {arr} -> Expected: {expected}, Got: {result}")

    test_eligible_positions()

    # Uncomment the line below to run the interactive solution
    # solution.solve_eligible_positions()