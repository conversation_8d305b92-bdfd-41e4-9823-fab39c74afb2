def generateMatrix(n):
    """
    Generate an n x n matrix filled with elements from 1 to n^2 in spiral order.
    
    Args:
        n: int - Size of the matrix
        
    Returns:
        List[List[int]] - n x n matrix filled in spiral order
    """
    # Initialize an n x n matrix with zeros
    matrix = [[0 for _ in range(n)] for _ in range(n)]
    
    # Define the boundaries of the spiral
    top = 0
    right = n - 1
    bottom = n - 1
    left = 0
    
    # Current number to be placed in the matrix
    num = 1
    
    # Direction: 0 = right, 1 = down, 2 = left, 3 = up
    direction = 0
    
    # Fill the matrix in spiral order
    while num <= n * n:
        if direction == 0:  # Moving right
            for i in range(left, right + 1):
                matrix[top][i] = num
                num += 1
            top += 1  # Update top boundary
        
        elif direction == 1:  # Moving down
            for i in range(top, bottom + 1):
                matrix[i][right] = num
                num += 1
            right -= 1  # Update right boundary
        
        elif direction == 2:  # Moving left
            for i in range(right, left - 1, -1):
                matrix[bottom][i] = num
                num += 1
            bottom -= 1  # Update bottom boundary
        
        elif direction == 3:  # Moving up
            for i in range(bottom, top - 1, -1):
                matrix[i][left] = num
                num += 1
            left += 1  # Update left boundary
        
        # Change direction (0 -> 1 -> 2 -> 3 -> 0)
        direction = (direction + 1) % 4
    
    return matrix

# Test cases
test_cases = [1, 2, 3, 4, 5]

# Function to print matrix in a readable format
def print_matrix(matrix):
    for row in matrix:
        print(row)
    print()

# Run test cases
for n in test_cases:
    print(f"Spiral Matrix for n = {n}:")
    result = generateMatrix(n)
    print_matrix(result)