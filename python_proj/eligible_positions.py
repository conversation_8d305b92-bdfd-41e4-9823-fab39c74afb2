def is_eligible(arr, pos):
    """
    Check if position 'pos' is eligible to hold the sum of all elements.
    
    Args:
        arr: List of integers
        pos: Position to check
        
    Returns:
        bool: True if position is eligible, False otherwise
    """
    n = len(arr)
    # Create a copy of the array for simulation
    simulation = arr.copy()
    
    # If the element at pos is already 0, it can't be eligible
    if simulation[pos] == 0:
        return False
    
    # Try all possible sequences of operations
    remaining = set(range(n))
    
    while len(remaining) > 1:
        if pos not in remaining:
            return False
        
        # Find another position to operate with
        other_positions = [p for p in remaining if p != pos]
        if not other_positions:
            break
        
        # Try each possible operation
        found_valid_op = False
        for other_pos in other_positions:
            # Try operation between pos and other_pos
            if simulation[pos] > simulation[other_pos]:
                # pos > other: pos += other, other = 0
                simulation[pos] += simulation[other_pos]
                simulation[other_pos] = 0
                remaining.remove(other_pos)
                found_valid_op = True
                break
            elif simulation[pos] < simulation[other_pos]:
                # pos < other: pos = 0, other += pos
                simulation[other_pos] += simulation[pos]
                simulation[pos] = 0
                remaining.remove(pos)
                return False  # pos can't be eligible anymore
            else:  # simulation[pos] == simulation[other_pos]
                # Equal: pos += other, other = 0
                simulation[pos] += simulation[other_pos]
                simulation[other_pos] = 0
                remaining.remove(other_pos)
                found_valid_op = True
                break
        
        if not found_valid_op:
            return False
    
    # Check if pos is the only remaining position with non-zero value
    return pos in remaining and simulation[pos] == sum(arr)

def count_eligible_positions(arr):
    """
    Count the number of eligible positions in the array.
    
    An eligible position is one that can hold the sum of all elements
    after a series of operations as described in the problem.
    """
    eligible_count = 0
    for i in range(len(arr)):
        if is_eligible(arr, i):
            eligible_count += 1
    
    return eligible_count

def solve_test_cases():
    """Process multiple test cases"""
    t = int(input())  # Number of test cases
    
    for _ in range(t):
        n = int(input())  # Size of the array
        arr = list(map(int, input().split()))  # Array elements
        
        result = count_eligible_positions(arr)
        print(result)


# For testing with the provided examples
def test_with_examples():
    """Test the solution with the provided examples"""
    test_cases = [
        [1, 9, 8],
        [1, 2, 3, 4, 10000],
        [2, 2, 2, 2]
    ]
    
    for i, arr in enumerate(test_cases):
        result = count_eligible_positions(arr)
        print(f"Test case {i+1}: {arr}")
        print(f"Result: {result}")
        print()

# For running with example test cases
test_with_examples()

# For submitting to the online judge, uncomment this line and comment out the test_with_examples() line
# solve_test_cases()