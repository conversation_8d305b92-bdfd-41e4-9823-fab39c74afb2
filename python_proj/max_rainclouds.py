def max_rainclouds(clouds):
    """
    Find the maximum number of rainclouds that can be selected without causing any patch to flood.
    
    Args:
        clouds: List of tuples (start, end) representing the boundaries of each raincloud
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Find the maximum end point to determine the size of our coverage array
    max_end = max(cloud[1] for cloud in clouds)
    
    # Try all possible subsets of rainclouds
    return backtrack(clouds, [], 0, [0] * (max_end + 1))

def backtrack(clouds, selected, index, coverage):
    """
    Backtracking function to try all possible combinations of rainclouds.
    
    Args:
        clouds: List of rainclouds
        selected: Currently selected rainclouds
        index: Current index in the clouds list
        coverage: Array tracking how many rainclouds cover each point
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Base case: we've considered all rainclouds
    if index == len(clouds):
        return len(selected)
    
    # Option 1: Skip the current raincloud
    max_count = backtrack(clouds, selected, index + 1, coverage)
    
    # Option 2: Include the current raincloud if it doesn't cause flooding
    start, end = clouds[index]
    can_include = True
    
    # Check if adding this raincloud would cause any patch to be covered by more than 2 rainclouds
    for i in range(start, end + 1):
        if coverage[i] >= 2:
            can_include = False
            break
    
    if can_include:
        # Update coverage
        for i in range(start, end + 1):
            coverage[i] += 1
        
        # Include this raincloud and continue
        selected.append(index)
        include_count = backtrack(clouds, selected, index + 1, coverage)
        
        # Backtrack: remove this raincloud
        selected.pop()
        for i in range(start, end + 1):
            coverage[i] -= 1
        
        max_count = max(max_count, include_count)
    
    return max_count

def solve_test_cases():
    """Process multiple test cases"""
    t = int(input())  # Number of test cases
    
    for _ in range(t):
        n = int(input())  # Number of rainclouds
        clouds = []
        
        for _ in range(n):
            start, end = map(int, input().split())
            clouds.append((start, end))
        
        result = max_rainclouds(clouds)
        print(result)

# For testing with the provided examples
def test_with_examples():
    """Test the solution with the provided examples"""
    test_cases = [
        [(1, 2), (2, 3), (2, 4)],
        [(1, 5), (1, 5), (1, 5)],
        [(1, 10), (1, 3), (4, 6), (7, 10)],
        [(1, 10), (1, 3), (3, 6), (7, 10)]
    ]
    
    for i, clouds in enumerate(test_cases):
        result = max_rainclouds(clouds)
        print(f"Test case {i+1}: {clouds}")
        print(f"Result: {result}")
        print()

def max_rainclouds_optimized(clouds):
    """
    Optimized approach using sweep line algorithm with events.

    Time Complexity: O(n^2) - much better than previous O(n^2 * max_coordinate)
    Space Complexity: O(n)

    Key insight: Instead of tracking coverage at every point, we only need to
    check if adding a new interval would create any point with >2 coverage.
    """
    if not clouds:
        return 0

    # Sort by end time (greedy choice)
    clouds.sort(key=lambda x: x[1])

    selected = []

    for start, end in clouds:
        # Check if this interval can be added
        if can_add_interval_optimized(selected, start, end):
            selected.append((start, end))

    return len(selected)

def can_add_interval_optimized(selected_intervals, new_start, new_end):
    """
    Check if adding interval [new_start, new_end] would violate capacity constraint.

    Optimized version: Instead of checking every point, we use interval intersection logic.
    """
    # Count overlapping intervals at critical points
    critical_points = set([new_start, new_end])

    # Add all start and end points of selected intervals that might overlap
    for start, end in selected_intervals:
        if not (end < new_start or start > new_end):  # Intervals overlap
            critical_points.add(start)
            critical_points.add(end)
            critical_points.add(max(start, new_start))
            critical_points.add(min(end, new_end))

    # Check coverage at each critical point
    for point in critical_points:
        if new_start <= point <= new_end:
            coverage = 1  # The new interval covers this point

            # Count how many selected intervals also cover this point
            for start, end in selected_intervals:
                if start <= point <= end:
                    coverage += 1

            if coverage > 2:
                return False

    return True

def max_rainclouds_greedy_v2(clouds):
    """
    Even more optimized: Use interval scheduling with capacity 2.

    Time Complexity: O(n^2) in worst case, but much faster in practice
    Space Complexity: O(n)
    """
    if not clouds:
        return 0

    # Sort by end time
    clouds.sort(key=lambda x: x[1])

    selected = []

    for start, end in clouds:
        # For each selected interval, check if they overlap
        overlapping_intervals = []
        for sel_start, sel_end in selected:
            if not (sel_end < start or sel_start > end):  # They overlap
                overlapping_intervals.append((sel_start, sel_end))

        # If we have 2 or more overlapping intervals, we can't add this one
        if len(overlapping_intervals) >= 2:
            continue

        # If we have exactly 1 overlapping interval, check if they can coexist
        if len(overlapping_intervals) == 1:
            # They can coexist since each point can have up to 2 intervals
            selected.append((start, end))
        else:
            # No overlapping intervals, safe to add
            selected.append((start, end))

    return len(selected)

def test_both_approaches():
    """Test both backtracking and greedy approaches"""
    test_cases = [
        [(1, 2), (2, 3), (2, 4)],
        [(1, 5), (1, 5), (1, 5)],
        [(1, 10), (1, 3), (4, 6), (7, 10)],
        [(1, 10), (1, 3), (3, 6), (7, 10)]
    ]

    expected = [2, 2, 4, 3]

    print("Comparing Backtracking vs Greedy approaches:")
    print("=" * 50)

    for i, clouds in enumerate(test_cases):
        backtrack_result = max_rainclouds(clouds)
        greedy_result = max_rainclouds_greedy_v2(clouds)

        print(f"Test case {i+1}: {clouds}")
        print(f"Expected: {expected[i]}")
        print(f"Backtracking: {backtrack_result}")
        print(f"Greedy: {greedy_result}")
        print(f"Match: {backtrack_result == greedy_result == expected[i]}")
        print()

def solve_test_cases_greedy():
    """Process multiple test cases using the greedy approach"""
    t = int(input())  # Number of test cases

    for _ in range(t):
        n = int(input())  # Number of rainclouds
        clouds = []

        for _ in range(n):
            start, end = map(int, input().split())
            clouds.append((start, end))

        # Use greedy approach for better performance
        result = max_rainclouds_greedy(clouds)
        print(result)

# For running with example test cases
# test_both_approaches()

# For submitting to the online judge
solve_test_cases_greedy()