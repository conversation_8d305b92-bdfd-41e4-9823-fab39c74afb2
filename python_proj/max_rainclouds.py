def max_rainclouds(clouds):
    """
    Find the maximum number of rainclouds that can be selected without causing any patch to flood.
    
    Args:
        clouds: List of tuples (start, end) representing the boundaries of each raincloud
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Find the maximum end point to determine the size of our coverage array
    max_end = max(cloud[1] for cloud in clouds)
    
    # Try all possible subsets of rainclouds
    return backtrack(clouds, [], 0, [0] * (max_end + 1))

def backtrack(clouds, selected, index, coverage):
    """
    Backtracking function to try all possible combinations of rainclouds.
    
    Args:
        clouds: List of rainclouds
        selected: Currently selected rainclouds
        index: Current index in the clouds list
        coverage: Array tracking how many rainclouds cover each point
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Base case: we've considered all rainclouds
    if index == len(clouds):
        return len(selected)
    
    # Option 1: Skip the current raincloud
    max_count = backtrack(clouds, selected, index + 1, coverage)
    
    # Option 2: Include the current raincloud if it doesn't cause flooding
    start, end = clouds[index]
    can_include = True
    
    # Check if adding this raincloud would cause any patch to be covered by more than 2 rainclouds
    for i in range(start, end + 1):
        if coverage[i] >= 2:
            can_include = False
            break
    
    if can_include:
        # Update coverage
        for i in range(start, end + 1):
            coverage[i] += 1
        
        # Include this raincloud and continue
        selected.append(index)
        include_count = backtrack(clouds, selected, index + 1, coverage)
        
        # Backtrack: remove this raincloud
        selected.pop()
        for i in range(start, end + 1):
            coverage[i] -= 1
        
        max_count = max(max_count, include_count)
    
    return max_count

def solve_test_cases():
    """Process multiple test cases"""
    t = int(input())  # Number of test cases
    
    for _ in range(t):
        n = int(input())  # Number of rainclouds
        clouds = []
        
        for _ in range(n):
            start, end = map(int, input().split())
            clouds.append((start, end))
        
        result = max_rainclouds(clouds)
        print(result)

# For testing with the provided examples
def test_with_examples():
    """Test the solution with the provided examples"""
    test_cases = [
        [(1, 2), (2, 3), (2, 4)],
        [(1, 5), (1, 5), (1, 5)],
        [(1, 10), (1, 3), (4, 6), (7, 10)],
        [(1, 10), (1, 3), (3, 6), (7, 10)]
    ]
    
    for i, clouds in enumerate(test_cases):
        result = max_rainclouds(clouds)
        print(f"Test case {i+1}: {clouds}")
        print(f"Result: {result}")
        print()

def max_rainclouds_greedy(clouds):
    """
    Greedy approach: Sort by end time and select greedily.
    This is more efficient than backtracking for larger inputs.

    Time Complexity: O(n^2 * max_coordinate) in worst case
    Space Complexity: O(max_coordinate)
    """
    if not clouds:
        return 0

    # Sort by end time (greedy choice)
    clouds.sort(key=lambda x: x[1])

    # Find the maximum coordinate to create coverage array
    max_coord = max(max(cloud) for cloud in clouds)
    coverage = [0] * (max_coord + 1)

    selected_count = 0

    for start, end in clouds:
        # Check if we can add this cloud
        can_add = True
        for i in range(start, end + 1):
            if coverage[i] >= 2:
                can_add = False
                break

        if can_add:
            # Add this cloud
            for i in range(start, end + 1):
                coverage[i] += 1
            selected_count += 1

    return selected_count

def test_both_approaches():
    """Test both backtracking and greedy approaches"""
    test_cases = [
        [(1, 2), (2, 3), (2, 4)],
        [(1, 5), (1, 5), (1, 5)],
        [(1, 10), (1, 3), (4, 6), (7, 10)],
        [(1, 10), (1, 3), (3, 6), (7, 10)]
    ]

    expected = [2, 2, 4, 3]

    print("Comparing Backtracking vs Greedy approaches:")
    print("=" * 50)

    for i, clouds in enumerate(test_cases):
        backtrack_result = max_rainclouds(clouds)
        greedy_result = max_rainclouds_greedy(clouds)

        print(f"Test case {i+1}: {clouds}")
        print(f"Expected: {expected[i]}")
        print(f"Backtracking: {backtrack_result}")
        print(f"Greedy: {greedy_result}")
        print(f"Match: {backtrack_result == greedy_result == expected[i]}")
        print()

# For running with example test cases
test_both_approaches()

# For submitting to the online judge, uncomment this line and comment out the test above
# solve_test_cases()