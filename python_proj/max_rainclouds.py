def max_rainclouds(clouds):
    """
    Find the maximum number of rainclouds that can be selected without causing any patch to flood.
    
    Args:
        clouds: List of tuples (start, end) representing the boundaries of each raincloud
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Find the maximum end point to determine the size of our coverage array
    max_end = max(cloud[1] for cloud in clouds)
    
    # Try all possible subsets of rainclouds
    return backtrack(clouds, [], 0, [0] * (max_end + 1))

def backtrack(clouds, selected, index, coverage):
    """
    Backtracking function to try all possible combinations of rainclouds.
    
    Args:
        clouds: List of rainclouds
        selected: Currently selected rainclouds
        index: Current index in the clouds list
        coverage: Array tracking how many rainclouds cover each point
        
    Returns:
        int: Maximum number of rainclouds that can be selected
    """
    # Base case: we've considered all rainclouds
    if index == len(clouds):
        return len(selected)
    
    # Option 1: Skip the current raincloud
    max_count = backtrack(clouds, selected, index + 1, coverage)
    
    # Option 2: Include the current raincloud if it doesn't cause flooding
    start, end = clouds[index]
    can_include = True
    
    # Check if adding this raincloud would cause any patch to be covered by more than 2 rainclouds
    for i in range(start, end + 1):
        if coverage[i] >= 2:
            can_include = False
            break
    
    if can_include:
        # Update coverage
        for i in range(start, end + 1):
            coverage[i] += 1
        
        # Include this raincloud and continue
        selected.append(index)
        include_count = backtrack(clouds, selected, index + 1, coverage)
        
        # Backtrack: remove this raincloud
        selected.pop()
        for i in range(start, end + 1):
            coverage[i] -= 1
        
        max_count = max(max_count, include_count)
    
    return max_count

def solve_test_cases():
    """Process multiple test cases"""
    t = int(input())  # Number of test cases
    
    for _ in range(t):
        n = int(input())  # Number of rainclouds
        clouds = []
        
        for _ in range(n):
            start, end = map(int, input().split())
            clouds.append((start, end))
        
        result = max_rainclouds(clouds)
        print(result)

# For testing with the provided examples
def test_with_examples():
    """Test the solution with the provided examples"""
    test_cases = [
        [(1, 2), (2, 3), (2, 4)],
        [(1, 5), (1, 5), (1, 5)],
        [(1, 10), (1, 3), (4, 6), (7, 10)],
        [(1, 10), (1, 3), (3, 6), (7, 10)]
    ]
    
    for i, clouds in enumerate(test_cases):
        result = max_rainclouds(clouds)
        print(f"Test case {i+1}: {clouds}")
        print(f"Result: {result}")
        print()

# For running with example test cases
test_with_examples()

# For submitting to the online judge, uncomment this line and comment out the test_with_examples() line
# solve_test_cases()