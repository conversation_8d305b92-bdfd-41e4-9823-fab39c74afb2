# Food Delivery App

A comprehensive food delivery mobile application built with React Native, featuring customer, restaurant, and delivery partner interfaces.

## Architecture Overview

This app implements a multi-sided food delivery platform with:
- **Customer Mobile App**: Browse restaurants, place orders, track deliveries
- **Restaurant Dashboard**: Manage orders, menus, and analytics
- **Delivery Partner App**: Accept orders, navigate, track earnings
- **Backend Microservices**: User, Restaurant, Order, Payment, Delivery, Notification services

## Prerequisites

Before running this app, ensure you have:

### Required Software
1. **Node.js** (v16 or higher)
   ```bash
   # Install using Homebrew (macOS)
   brew install node
   
   # Or download from https://nodejs.org/
   ```

2. **React Native CLI**
   ```bash
   npm install -g react-native-cli
   ```

3. **Development Environment**
   - **For iOS**: Xcode (macOS only)
   - **For Android**: Android Studio with Android SDK

4. **Additional Tools**
   ```bash
   # Install Watchman (recommended for macOS)
   brew install watchman
   
   # Install CocoaPods (for iOS dependencies)
   sudo gem install cocoapods
   ```

## Quick Start

### 1. Install Dependencies
```bash
# Navigate to project directory
cd food-delivery-app

# Install Node.js dependencies
npm install

# Install iOS dependencies (macOS only)
cd ios && pod install && cd ..
```

### 2. Start Metro Bundler
```bash
npm start
```

### 3. Run the App

#### For Android
```bash
# Make sure Android emulator is running or device is connected
npm run android
```

#### For iOS (macOS only)
```bash
# Make sure iOS simulator is running
npm run ios
```

## Project Structure

```
food-delivery-app/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # App screens
│   │   ├── customer/       # Customer app screens
│   │   ├── restaurant/     # Restaurant dashboard screens
│   │   └── delivery/       # Delivery partner screens
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API services and external integrations
│   ├── store/             # Redux store configuration
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript type definitions
│   └── assets/            # Images, fonts, etc.
├── backend/               # Backend microservices
├── android/              # Android-specific code
├── ios/                  # iOS-specific code
└── __tests__/           # Test files
```

## Features

### Customer App
- [x] User registration and authentication
- [x] Restaurant browsing and search
- [x] Menu viewing and ordering
- [x] Real-time order tracking
- [x] Payment integration
- [x] Order history
- [x] Push notifications

### Restaurant Dashboard
- [x] Order management
- [x] Menu management
- [x] Analytics and reporting
- [x] Profile management

### Delivery Partner App
- [x] Order assignment
- [x] GPS navigation
- [x] Earnings tracking
- [x] Delivery history

## External Services Integration

### Required API Keys
Create a `.env` file in the root directory with:

```env
# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Firebase
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_project_id

# Payment Gateways
STRIPE_PUBLISHABLE_KEY=your_stripe_key
RAZORPAY_KEY_ID=your_razorpay_key

# SMS Service
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token

# Backend API
API_BASE_URL=http://localhost:3000/api
```

## Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run E2E tests (requires Detox setup)
npm run test:e2e
```

## Building for Production

### Android APK
```bash
npm run build:android
# APK will be generated in android/app/build/outputs/apk/release/
```

### iOS Archive (macOS only)
```bash
npm run build:ios
# Archive will be created for App Store submission
```

## Play Store Deployment

### 1. Prepare Release Build
- Update version in `package.json` and `android/app/build.gradle`
- Generate signed APK or AAB (Android App Bundle)
- Test thoroughly on different devices

### 2. Play Store Console Setup
1. Create developer account at https://play.google.com/console
2. Create new app listing
3. Upload APK/AAB
4. Complete store listing (descriptions, screenshots, etc.)
5. Set up content rating and pricing
6. Submit for review

### 3. App Store Connect (iOS)
1. Create developer account at https://developer.apple.com
2. Generate certificates and provisioning profiles
3. Archive and upload to App Store Connect
4. Complete app metadata
5. Submit for review

## Monitoring and Analytics

- **Crashlytics**: Crash reporting and analytics
- **Firebase Analytics**: User behavior tracking
- **Performance Monitoring**: App performance metrics

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
