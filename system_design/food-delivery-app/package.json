{"name": "food-delivery-app", "version": "1.0.0", "description": "A comprehensive food delivery mobile application", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace FoodDeliveryApp.xcworkspace -scheme FoodDeliveryApp -configuration Release -destination generic/platform=iOS -archivePath FoodDeliveryApp.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.14.0", "react-native-reanimated": "^3.6.0", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "react-native-async-storage": "@react-native-async-storage/async-storage", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^3.10.1", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/auth": "^18.6.1", "react-native-vector-icons": "^10.0.2", "react-native-image-picker": "^7.0.3", "react-native-fast-image": "^8.6.3", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^13.14.0", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "react-native-stripe-sdk": "^10.1.0", "react-native-razorpay": "^2.3.0", "react-native-otp-verify": "^1.0.4", "react-native-device-info": "^10.11.0", "react-native-keychain": "^8.1.3", "react-native-share": "^9.4.1", "react-native-rate": "^1.2.12"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.0", "@react-native/metro-config": "^0.73.0", "@react-native/typescript-config": "^0.73.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "@testing-library/react-native": "^12.4.0", "@testing-library/jest-native": "^5.4.3", "detox": "^20.13.0"}, "engines": {"node": ">=16"}}