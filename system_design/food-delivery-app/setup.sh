#!/bin/bash

# Food Delivery App Setup Script
# This script sets up the development environment for the food delivery app

set -e

echo "🍕 Food Delivery App Setup Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if running on macOS or Linux
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

print_info "Detected OS: $MACHINE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js installation
check_nodejs() {
    print_info "Checking Node.js installation..."
    
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_status "Node.js is installed: $NODE_VERSION"
        
        # Check if version is >= 16
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
            print_warning "Node.js version should be 16 or higher. Current: $NODE_VERSION"
            print_info "Please update Node.js: https://nodejs.org/"
        fi
    else
        print_error "Node.js is not installed"
        print_info "Installing Node.js..."
        
        if [ "$MACHINE" = "Mac" ]; then
            if command_exists brew; then
                brew install node
            else
                print_error "Homebrew not found. Please install Node.js manually: https://nodejs.org/"
                exit 1
            fi
        else
            print_info "Please install Node.js manually: https://nodejs.org/"
            exit 1
        fi
    fi
}

# Check npm installation
check_npm() {
    print_info "Checking npm installation..."
    
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_status "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed"
        exit 1
    fi
}

# Install React Native CLI
install_react_native_cli() {
    print_info "Installing React Native CLI..."
    
    if command_exists react-native; then
        print_status "React Native CLI is already installed"
    else
        npm install -g react-native-cli @react-native-community/cli
        print_status "React Native CLI installed"
    fi
}

# Check Android development setup
check_android_setup() {
    print_info "Checking Android development setup..."
    
    if [ -z "$ANDROID_HOME" ]; then
        print_warning "ANDROID_HOME environment variable is not set"
        print_info "Please install Android Studio and set up Android SDK"
        print_info "Add these lines to your ~/.bashrc or ~/.zshrc:"
        echo "export ANDROID_HOME=\$HOME/Library/Android/sdk"
        echo "export PATH=\$PATH:\$ANDROID_HOME/emulator"
        echo "export PATH=\$PATH:\$ANDROID_HOME/tools"
        echo "export PATH=\$PATH:\$ANDROID_HOME/tools/bin"
        echo "export PATH=\$PATH:\$ANDROID_HOME/platform-tools"
    else
        print_status "ANDROID_HOME is set: $ANDROID_HOME"
    fi
    
    if command_exists adb; then
        print_status "Android Debug Bridge (adb) is available"
    else
        print_warning "Android Debug Bridge (adb) is not available"
    fi
}

# Check iOS development setup (macOS only)
check_ios_setup() {
    if [ "$MACHINE" = "Mac" ]; then
        print_info "Checking iOS development setup..."
        
        if command_exists xcodebuild; then
            XCODE_VERSION=$(xcodebuild -version | head -n1)
            print_status "Xcode is installed: $XCODE_VERSION"
        else
            print_warning "Xcode is not installed"
            print_info "Please install Xcode from the Mac App Store"
        fi
        
        if command_exists pod; then
            POD_VERSION=$(pod --version)
            print_status "CocoaPods is installed: $POD_VERSION"
        else
            print_info "Installing CocoaPods..."
            sudo gem install cocoapods
            print_status "CocoaPods installed"
        fi
    else
        print_info "iOS development is only available on macOS"
    fi
}

# Install project dependencies
install_dependencies() {
    print_info "Installing project dependencies..."
    
    # Install main project dependencies
    print_info "Installing React Native dependencies..."
    npm install
    print_status "React Native dependencies installed"
    
    # Install iOS dependencies (macOS only)
    if [ "$MACHINE" = "Mac" ] && [ -d "ios" ]; then
        print_info "Installing iOS dependencies..."
        cd ios && pod install && cd ..
        print_status "iOS dependencies installed"
    fi
    
    # Install backend dependencies
    if [ -d "backend" ]; then
        print_info "Installing backend dependencies..."
        cd backend && npm install && cd ..
        print_status "Backend dependencies installed"
    fi
}

# Create environment file
create_env_file() {
    print_info "Creating environment configuration..."
    
    if [ ! -f ".env" ]; then
        cat > .env << EOL
# API Configuration
API_BASE_URL=http://localhost:3000/api

# Google Maps API Key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_MESSAGING_SENDER_ID=your_firebase_sender_id_here
FIREBASE_APP_ID=your_firebase_app_id_here

# Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
RAZORPAY_KEY_ID=rzp_test_your_razorpay_key_here

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# App Configuration
APP_VERSION=1.0.0
BUILD_NUMBER=1
EOL
        print_status "Environment file created (.env)"
        print_warning "Please update the .env file with your actual API keys"
    else
        print_status "Environment file already exists"
    fi
}

# Setup git hooks
setup_git_hooks() {
    print_info "Setting up git hooks..."
    
    if [ -d ".git" ]; then
        # Install husky for git hooks
        npm install --save-dev husky lint-staged
        npx husky install
        npx husky add .husky/pre-commit "npx lint-staged"
        
        # Create lint-staged configuration
        cat > .lintstagedrc.json << EOL
{
  "*.{js,jsx,ts,tsx}": [
    "eslint --fix",
    "prettier --write"
  ],
  "*.{json,md}": [
    "prettier --write"
  ]
}
EOL
        print_status "Git hooks configured"
    else
        print_info "Not a git repository, skipping git hooks setup"
    fi
}

# Verify installation
verify_installation() {
    print_info "Verifying installation..."
    
    # Check if we can start Metro bundler
    print_info "Testing Metro bundler..."
    timeout 10s npm start > /dev/null 2>&1 || true
    print_status "Metro bundler test completed"
    
    # Run tests
    print_info "Running tests..."
    npm test -- --watchAll=false > /dev/null 2>&1 || true
    print_status "Test suite completed"
}

# Print next steps
print_next_steps() {
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Update the .env file with your actual API keys"
    echo "2. Set up your backend services (database, Redis, etc.)"
    echo "3. Configure Firebase project and download config files"
    echo "4. Set up Google Maps API and get API key"
    echo "5. Configure payment gateways (Stripe, Razorpay)"
    echo ""
    echo "To start development:"
    echo "• Start Metro bundler: npm start"
    echo "• Run on Android: npm run android"
    echo "• Run on iOS: npm run ios (macOS only)"
    echo "• Start backend: cd backend && npm run dev"
    echo ""
    echo "For more information, check:"
    echo "• README.md - Project overview and setup"
    echo "• DEPLOYMENT_GUIDE.md - Deployment instructions"
    echo "• TESTING_GUIDE.md - Testing guidelines"
    echo ""
    print_status "Happy coding! 🚀"
}

# Main execution
main() {
    echo ""
    print_info "Starting setup process..."
    echo ""
    
    check_nodejs
    check_npm
    install_react_native_cli
    check_android_setup
    check_ios_setup
    install_dependencies
    create_env_file
    setup_git_hooks
    verify_installation
    print_next_steps
}

# Run main function
main
