import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {Restaurant} from '../store/slices/restaurantSlice';
import {COLORS} from '../config/constants';

interface RestaurantCardProps {
  restaurant: Restaurant;
  onPress: (restaurant: Restaurant) => void;
  style?: ViewStyle;
  testID?: string;
}

const RestaurantCard: React.FC<RestaurantCardProps> = ({
  restaurant,
  onPress,
  style,
  testID,
}) => {
  const {
    name,
    cuisine,
    rating,
    deliveryTime,
    deliveryFee,
    image,
    isOpen,
    featured,
    distance,
  } = restaurant;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => onPress(restaurant)}
      testID={testID}
      activeOpacity={0.8}>
      
      {/* Restaurant Image */}
      <View style={styles.imageContainer}>
        <Image
          source={{uri: image || 'https://via.placeholder.com/300x200/FF6B35/FFFFFF?text=Restaurant'}}
          style={styles.image}
          resizeMode="cover"
        />
        
        {/* Featured Badge */}
        {featured && (
          <View style={styles.featuredBadge}>
            <Icon name="star" size={12} color="white" />
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}
        
        {/* Closed Overlay */}
        {!isOpen && (
          <View style={styles.closedOverlay}>
            <Text style={styles.closedText}>Closed</Text>
          </View>
        )}
      </View>

      {/* Restaurant Info */}
      <View style={styles.infoContainer}>
        <View style={styles.headerRow}>
          <Text style={styles.name} numberOfLines={1}>
            {name}
          </Text>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={14} color="#FFD700" />
            <Text style={styles.rating}>{rating.toFixed(1)}</Text>
          </View>
        </View>

        <Text style={styles.cuisine} numberOfLines={1}>
          {Array.isArray(cuisine) ? cuisine.join(', ') : cuisine}
        </Text>

        <View style={styles.detailsRow}>
          <View style={styles.detailItem}>
            <Icon name="access-time" size={14} color={COLORS.textSecondary} />
            <Text style={styles.detailText}>{deliveryTime}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Icon name="delivery-dining" size={14} color={COLORS.textSecondary} />
            <Text style={styles.detailText}>
              {deliveryFee === 0 ? 'Free' : `$${deliveryFee.toFixed(2)}`}
            </Text>
          </View>
          
          {distance && (
            <View style={styles.detailItem}>
              <Icon name="location-on" size={14} color={COLORS.textSecondary} />
              <Text style={styles.detailText}>{distance.toFixed(1)} km</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  featuredBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featuredText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closedText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    padding: 12,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
    marginLeft: 2,
  },
  cuisine: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
    fontWeight: '500',
  },
});

export default RestaurantCard;
