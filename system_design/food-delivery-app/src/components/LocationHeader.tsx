import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {COLORS} from '../config/constants';

const LocationHeader: React.FC = () => {
  const currentAddress = "123 Main St, San Francisco, CA"; // This would come from user's location

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <TouchableOpacity style={styles.locationContainer}>
          <Icon name="location-on" size={20} color={COLORS.primary} />
          <View style={styles.addressContainer}>
            <Text style={styles.deliverToText}>Deliver to</Text>
            <Text style={styles.addressText} numberOfLines={1}>
              {currentAddress}
            </Text>
          </View>
          <Icon name="keyboard-arrow-down" size={20} color={COLORS.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.notificationButton}>
          <Icon name="notifications" size={24} color={COLORS.text} />
          <View style={styles.notificationBadge}>
            <Text style={styles.badgeText}>3</Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: COLORS.surface,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  locationContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressContainer: {
    flex: 1,
    marginLeft: 8,
    marginRight: 8,
  },
  deliverToText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  addressText: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '600',
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: COLORS.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default LocationHeader;
