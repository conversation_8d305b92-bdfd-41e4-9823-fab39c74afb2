// API Configuration
export const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://your-production-api.com/api';

// App Configuration
export const APP_NAME = 'FoodDelivery';
export const APP_VERSION = '1.0.0';

// Colors
export const COLORS = {
  primary: '#FF6B35',
  secondary: '#4ECDC4',
  accent: '#45B7D1',
  background: '#F8F9FA',
  surface: '#FFFFFF',
  text: '#2C3E50',
  textSecondary: '#7F8C8D',
  error: '#E74C3C',
  success: '#27AE60',
  warning: '#F39C12',
  info: '#3498DB',
  
  // Gradients
  primaryGradient: ['#FF6B35', '#FF8A50'],
  secondaryGradient: ['#4ECDC4', '#44A08D'],
};

// Fonts
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

// Sizes
export const SIZES = {
  // Global sizes
  base: 8,
  font: 14,
  radius: 12,
  padding: 16,
  margin: 16,

  // Font sizes
  largeTitle: 40,
  h1: 30,
  h2: 22,
  h3: 16,
  h4: 14,
  body1: 30,
  body2: 22,
  body3: 16,
  body4: 14,
  body5: 12,

  // App dimensions
  width: 375,
  height: 812,
};

// External Service Keys (These should be in environment variables)
export const GOOGLE_MAPS_API_KEY = 'your_google_maps_api_key';
export const FIREBASE_CONFIG = {
  apiKey: 'your_firebase_api_key',
  authDomain: 'your_project.firebaseapp.com',
  projectId: 'your_project_id',
  storageBucket: 'your_project.appspot.com',
  messagingSenderId: 'your_sender_id',
  appId: 'your_app_id',
};

// Payment Configuration
export const STRIPE_PUBLISHABLE_KEY = 'pk_test_your_stripe_key';
export const RAZORPAY_KEY_ID = 'rzp_test_your_razorpay_key';

// Map Configuration
export const DEFAULT_REGION = {
  latitude: 37.78825,
  longitude: -122.4324,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PREPARING: 'preparing',
  READY: 'ready',
  PICKED_UP: 'picked_up',
  ON_THE_WAY: 'on_the_way',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
};

// User Types
export const USER_TYPES = {
  CUSTOMER: 'customer',
  RESTAURANT: 'restaurant',
  DELIVERY: 'delivery',
  ADMIN: 'admin',
};

// Delivery Configuration
export const DELIVERY_CONFIG = {
  BASE_FEE: 2.99,
  PER_KM_RATE: 0.5,
  FREE_DELIVERY_THRESHOLD: 25,
  MAX_DELIVERY_DISTANCE: 10, // km
  ESTIMATED_PREP_TIME: 30, // minutes
  ESTIMATED_DELIVERY_TIME: 20, // minutes
};

// App Store Configuration
export const APP_STORE_CONFIG = {
  ANDROID_PACKAGE_NAME: 'com.fooddelivery.app',
  IOS_APP_ID: '123456789',
  PLAY_STORE_URL: 'https://play.google.com/store/apps/details?id=com.fooddelivery.app',
  APP_STORE_URL: 'https://apps.apple.com/app/id123456789',
};

// Social Media
export const SOCIAL_MEDIA = {
  FACEBOOK: 'https://facebook.com/fooddeliveryapp',
  TWITTER: 'https://twitter.com/fooddeliveryapp',
  INSTAGRAM: 'https://instagram.com/fooddeliveryapp',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '******-123-4567',
};

// Cache Configuration
export const CACHE_CONFIG = {
  RESTAURANTS_TTL: 300000, // 5 minutes
  USER_PROFILE_TTL: 600000, // 10 minutes
  MENU_TTL: 900000, // 15 minutes
};

// Notification Types
export const NOTIFICATION_TYPES = {
  ORDER_CONFIRMED: 'order_confirmed',
  ORDER_PREPARING: 'order_preparing',
  ORDER_READY: 'order_ready',
  ORDER_PICKED_UP: 'order_picked_up',
  ORDER_DELIVERED: 'order_delivered',
  ORDER_CANCELLED: 'order_cancelled',
  PROMOTION: 'promotion',
  GENERAL: 'general',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Session expired. Please login again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  LOCATION_PERMISSION: 'Location permission is required for delivery.',
  CAMERA_PERMISSION: 'Camera permission is required to take photos.',
  NOTIFICATION_PERMISSION: 'Notification permission is required for order updates.',
};
