import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import {useRoute} from '@react-navigation/native';
import {COLORS} from '../../config/constants';

const RestaurantDetailScreen: React.FC = () => {
  const route = useRoute();
  const {restaurantId} = route.params as {restaurantId: string};

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Restaurant Details</Text>
        <Text style={styles.subtitle}>Restaurant ID: {restaurantId}</Text>
        <Text style={styles.description}>
          This screen will show restaurant details, menu, and allow ordering.
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default RestaurantDetailScreen;
