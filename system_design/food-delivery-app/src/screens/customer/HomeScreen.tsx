import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useSelector, useDispatch} from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import {RootState, AppDispatch} from '../../store';
import {fetchNearbyRestaurants} from '../../store/slices/restaurantSlice';
import RestaurantCard from '../../components/RestaurantCard';
import CategoryCard from '../../components/CategoryCard';
import SearchBar from '../../components/SearchBar';
import LocationHeader from '../../components/LocationHeader';

const {width} = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();
  const {user} = useSelector((state: RootState) => state.auth);
  const {nearbyRestaurants, isLoading} = useSelector((state: RootState) => state.restaurant);
  
  const [refreshing, setRefreshing] = useState(false);

  const categories = [
    {id: '1', name: 'Pizza', icon: '🍕', color: '#FF6B35'},
    {id: '2', name: 'Burger', icon: '🍔', color: '#4ECDC4'},
    {id: '3', name: 'Sushi', icon: '🍣', color: '#45B7D1'},
    {id: '4', name: 'Indian', icon: '🍛', color: '#F7DC6F'},
    {id: '5', name: 'Chinese', icon: '🥡', color: '#BB8FCE'},
    {id: '6', name: 'Dessert', icon: '🍰', color: '#F1948A'},
  ];

  const offers = [
    {
      id: '1',
      title: '50% OFF',
      subtitle: 'On your first order',
      image: 'https://via.placeholder.com/300x150/FF6B35/FFFFFF?text=50%25+OFF',
    },
    {
      id: '2',
      title: 'Free Delivery',
      subtitle: 'Orders above $25',
      image: 'https://via.placeholder.com/300x150/4ECDC4/FFFFFF?text=Free+Delivery',
    },
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await dispatch(fetchNearbyRestaurants()).unwrap();
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const renderOfferItem = ({item}: {item: any}) => (
    <TouchableOpacity style={styles.offerCard}>
      <Image source={{uri: item.image}} style={styles.offerImage} />
      <View style={styles.offerContent}>
        <Text style={styles.offerTitle}>{item.title}</Text>
        <Text style={styles.offerSubtitle}>{item.subtitle}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryItem = ({item}: {item: any}) => (
    <CategoryCard
      category={item}
      onPress={() => navigation.navigate('Search', {category: item.name})}
    />
  );

  const renderRestaurantItem = ({item}: {item: any}) => (
    <RestaurantCard
      restaurant={item}
      onPress={() => navigation.navigate('RestaurantDetail', {restaurantId: item.id})}
    />
  );

  return (
    <View style={styles.container}>
      <LocationHeader />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}>
        
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <SearchBar
            placeholder="Search for restaurants, cuisines..."
            onPress={() => navigation.navigate('Search')}
          />
        </View>

        {/* Greeting */}
        <View style={styles.greetingContainer}>
          <Text style={styles.greetingText}>
            Hello {user?.name?.split(' ')[0] || 'there'}! 👋
          </Text>
          <Text style={styles.subGreetingText}>
            What would you like to eat today?
          </Text>
        </View>

        {/* Offers */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Special Offers</Text>
          <FlatList
            data={offers}
            renderItem={renderOfferItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>

        {/* Categories */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>

        {/* Nearby Restaurants */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Restaurants</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Search')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {nearbyRestaurants.map((restaurant, index) => (
            <RestaurantCard
              key={restaurant.id}
              restaurant={restaurant}
              onPress={() => navigation.navigate('RestaurantDetail', {restaurantId: restaurant.id})}
              style={styles.restaurantCard}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  greetingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  subGreetingText: {
    fontSize: 16,
    color: '#7F8C8D',
  },
  sectionContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  seeAllText: {
    fontSize: 14,
    color: '#FF6B35',
    fontWeight: '600',
  },
  horizontalList: {
    paddingRight: 16,
  },
  offerCard: {
    width: width * 0.8,
    marginRight: 12,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  offerImage: {
    width: '100%',
    height: 120,
  },
  offerContent: {
    padding: 12,
  },
  offerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  offerSubtitle: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  restaurantCard: {
    marginBottom: 12,
  },
});

export default HomeScreen;
