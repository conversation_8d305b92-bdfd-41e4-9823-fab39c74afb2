import React from 'react';
import {View, Text, StyleSheet, SafeAreaView} from 'react-native';
import {COLORS} from '../../config/constants';

const DeliveryEarningsScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Earnings</Text>
        <Text style={styles.subtitle}>Track your delivery earnings and payments</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: COLORS.background},
  content: {flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 32},
  title: {fontSize: 24, fontWeight: 'bold', color: COLORS.text, marginBottom: 8},
  subtitle: {fontSize: 16, color: COLORS.textSecondary, textAlign: 'center'},
});

export default DeliveryEarningsScreen;
