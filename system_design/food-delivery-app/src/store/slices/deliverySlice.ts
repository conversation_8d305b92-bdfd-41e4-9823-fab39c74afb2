import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';

export interface DeliveryPartner {
  id: string;
  name: string;
  phone: string;
  email: string;
  profileImage?: string;
  rating: number;
  totalDeliveries: number;
  isOnline: boolean;
  currentLocation: {
    latitude: number;
    longitude: number;
  };
  vehicle: {
    type: 'bike' | 'scooter' | 'car';
    model: string;
    licensePlate: string;
  };
  earnings: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    total: number;
  };
  documents: {
    drivingLicense: string;
    vehicleRegistration: string;
    insurance: string;
  };
  isVerified: boolean;
  joinedAt: string;
}

export interface DeliveryOrder {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  restaurantId: string;
  restaurantName: string;
  restaurantAddress: string;
  restaurantLocation: {
    latitude: number;
    longitude: number;
  };
  deliveryAddress: string;
  deliveryLocation: {
    latitude: number;
    longitude: number;
  };
  items: DeliveryOrderItem[];
  total: number;
  deliveryFee: number;
  estimatedDistance: number;
  estimatedTime: number;
  status: DeliveryStatus;
  pickupTime?: string;
  deliveryTime?: string;
  specialInstructions?: string;
  createdAt: string;
}

export interface DeliveryOrderItem {
  name: string;
  quantity: number;
  price: number;
}

export type DeliveryStatus = 
  | 'available'
  | 'assigned'
  | 'accepted'
  | 'picked_up'
  | 'on_the_way'
  | 'delivered'
  | 'cancelled';

interface DeliveryState {
  partner: DeliveryPartner | null;
  availableOrders: DeliveryOrder[];
  activeOrder: DeliveryOrder | null;
  orderHistory: DeliveryOrder[];
  isOnline: boolean;
  currentLocation: {
    latitude: number;
    longitude: number;
  } | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: DeliveryState = {
  partner: null,
  availableOrders: [],
  activeOrder: null,
  orderHistory: [],
  isOnline: false,
  currentLocation: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchDeliveryPartnerProfile = createAsyncThunk(
  'delivery/fetchProfile',
  async (_, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/delivery/profile');
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateOnlineStatus = createAsyncThunk(
  'delivery/updateOnlineStatus',
  async (isOnline: boolean, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/delivery/status', {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({isOnline}),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchAvailableOrders = createAsyncThunk(
  'delivery/fetchAvailableOrders',
  async (location: {latitude: number; longitude: number}, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/delivery/orders/available?lat=${location.latitude}&lng=${location.longitude}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const acceptOrder = createAsyncThunk(
  'delivery/acceptOrder',
  async (orderId: string, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/delivery/orders/${orderId}/accept`, {
        method: 'POST',
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateOrderStatus = createAsyncThunk(
  'delivery/updateOrderStatus',
  async ({orderId, status}: {orderId: string; status: DeliveryStatus}, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/delivery/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({status}),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateLocation = createAsyncThunk(
  'delivery/updateLocation',
  async (location: {latitude: number; longitude: number}, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/delivery/location', {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(location),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchEarnings = createAsyncThunk(
  'delivery/fetchEarnings',
  async (period: 'today' | 'week' | 'month', {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/delivery/earnings?period=${period}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const deliverySlice = createSlice({
  name: 'delivery',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentLocation: (state, action: PayloadAction<{latitude: number; longitude: number}>) => {
      state.currentLocation = action.payload;
    },
    setActiveOrder: (state, action: PayloadAction<DeliveryOrder | null>) => {
      state.activeOrder = action.payload;
    },
    removeAvailableOrder: (state, action: PayloadAction<string>) => {
      state.availableOrders = state.availableOrders.filter(order => order.id !== action.payload);
    },
    addToOrderHistory: (state, action: PayloadAction<DeliveryOrder>) => {
      state.orderHistory.unshift(action.payload);
    },
    updatePartnerEarnings: (state, action: PayloadAction<Partial<DeliveryPartner['earnings']>>) => {
      if (state.partner) {
        state.partner.earnings = {
          ...state.partner.earnings,
          ...action.payload,
        };
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch delivery partner profile
    builder
      .addCase(fetchDeliveryPartnerProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDeliveryPartnerProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.partner = action.payload;
        state.isOnline = action.payload.isOnline;
      })
      .addCase(fetchDeliveryPartnerProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update online status
    builder
      .addCase(updateOnlineStatus.fulfilled, (state, action) => {
        state.isOnline = action.payload.isOnline;
        if (state.partner) {
          state.partner.isOnline = action.payload.isOnline;
        }
      });

    // Fetch available orders
    builder
      .addCase(fetchAvailableOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAvailableOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.availableOrders = action.payload;
      })
      .addCase(fetchAvailableOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Accept order
    builder
      .addCase(acceptOrder.fulfilled, (state, action) => {
        state.activeOrder = action.payload;
        state.availableOrders = state.availableOrders.filter(order => order.id !== action.payload.id);
      });

    // Update order status
    builder
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        if (state.activeOrder && state.activeOrder.id === action.payload.id) {
          state.activeOrder = action.payload;
          
          // If order is completed, move to history and clear active
          if (action.payload.status === 'delivered' || action.payload.status === 'cancelled') {
            state.orderHistory.unshift(action.payload);
            state.activeOrder = null;
          }
        }
      });

    // Update location
    builder
      .addCase(updateLocation.fulfilled, (state, action) => {
        state.currentLocation = action.payload.location;
        if (state.partner) {
          state.partner.currentLocation = action.payload.location;
        }
      });

    // Fetch earnings
    builder
      .addCase(fetchEarnings.fulfilled, (state, action) => {
        if (state.partner) {
          state.partner.earnings = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setCurrentLocation,
  setActiveOrder,
  removeAvailableOrder,
  addToOrderHistory,
  updatePartnerEarnings,
} = deliverySlice.actions;

export default deliverySlice.reducer;
