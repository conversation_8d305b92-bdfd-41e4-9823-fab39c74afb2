import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';

export interface Order {
  id: string;
  customerId: string;
  restaurantId: string;
  restaurantName: string;
  restaurantImage: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  tax: number;
  total: number;
  status: OrderStatus;
  deliveryAddress: DeliveryAddress;
  paymentMethod: PaymentMethod;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  deliveryPartnerId?: string;
  deliveryPartnerName?: string;
  deliveryPartnerPhone?: string;
  tracking: OrderTracking;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  promoCode?: string;
  discount?: number;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  customizations: OrderCustomization[];
  specialInstructions?: string;
  image?: string;
}

export interface OrderCustomization {
  id: string;
  name: string;
  options: string[];
  additionalPrice: number;
}

export interface DeliveryAddress {
  id: string;
  label: string;
  address: string;
  latitude: number;
  longitude: number;
  instructions?: string;
}

export interface PaymentMethod {
  type: 'card' | 'cash' | 'wallet';
  cardLast4?: string;
  cardBrand?: string;
}

export interface OrderTracking {
  orderPlaced: TrackingEvent;
  orderConfirmed?: TrackingEvent;
  preparingFood?: TrackingEvent;
  foodReady?: TrackingEvent;
  pickedUp?: TrackingEvent;
  onTheWay?: TrackingEvent;
  delivered?: TrackingEvent;
  cancelled?: TrackingEvent;
}

export interface TrackingEvent {
  timestamp: string;
  message: string;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'picked_up'
  | 'on_the_way'
  | 'delivered'
  | 'cancelled';

interface OrderState {
  orders: Order[];
  activeOrder: Order | null;
  orderHistory: Order[];
  isLoading: boolean;
  isPlacingOrder: boolean;
  error: string | null;
}

const initialState: OrderState = {
  orders: [],
  activeOrder: null,
  orderHistory: [],
  isLoading: false,
  isPlacingOrder: false,
  error: null,
};

// Async thunks
export const placeOrder = createAsyncThunk(
  'order/placeOrder',
  async (orderData: {
    restaurantId: string;
    items: OrderItem[];
    deliveryAddress: DeliveryAddress;
    paymentMethod: PaymentMethod;
    notes?: string;
    promoCode?: string;
  }, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(orderData),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchOrders = createAsyncThunk(
  'order/fetchOrders',
  async (_, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/orders');
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchOrderDetails = createAsyncThunk(
  'order/fetchOrderDetails',
  async (orderId: string, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'order/cancelOrder',
  async (orderId: string, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/cancel`, {
        method: 'POST',
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const trackOrder = createAsyncThunk(
  'order/trackOrder',
  async (orderId: string, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/track`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const rateOrder = createAsyncThunk(
  'order/rateOrder',
  async (ratingData: {
    orderId: string;
    restaurantRating: number;
    deliveryRating: number;
    comment?: string;
  }, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/orders/${ratingData.orderId}/rate`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(ratingData),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setActiveOrder: (state, action: PayloadAction<Order | null>) => {
      state.activeOrder = action.payload;
    },
    updateOrderStatus: (state, action: PayloadAction<{orderId: string; status: OrderStatus; tracking?: TrackingEvent}>) => {
      const {orderId, status, tracking} = action.payload;
      
      // Update in orders array
      const orderIndex = state.orders.findIndex(order => order.id === orderId);
      if (orderIndex !== -1) {
        state.orders[orderIndex].status = status;
        if (tracking) {
          state.orders[orderIndex].tracking = {
            ...state.orders[orderIndex].tracking,
            [status]: tracking,
          };
        }
      }
      
      // Update active order if it matches
      if (state.activeOrder && state.activeOrder.id === orderId) {
        state.activeOrder.status = status;
        if (tracking) {
          state.activeOrder.tracking = {
            ...state.activeOrder.tracking,
            [status]: tracking,
          };
        }
      }
    },
    clearActiveOrder: (state) => {
      state.activeOrder = null;
    },
  },
  extraReducers: (builder) => {
    // Place order
    builder
      .addCase(placeOrder.pending, (state) => {
        state.isPlacingOrder = true;
        state.error = null;
      })
      .addCase(placeOrder.fulfilled, (state, action) => {
        state.isPlacingOrder = false;
        state.orders.unshift(action.payload);
        state.activeOrder = action.payload;
      })
      .addCase(placeOrder.rejected, (state, action) => {
        state.isPlacingOrder = false;
        state.error = action.payload as string;
      });

    // Fetch orders
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.orders;
        state.orderHistory = action.payload.history;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch order details
    builder
      .addCase(fetchOrderDetails.fulfilled, (state, action) => {
        const orderIndex = state.orders.findIndex(order => order.id === action.payload.id);
        if (orderIndex !== -1) {
          state.orders[orderIndex] = action.payload;
        }
        if (state.activeOrder && state.activeOrder.id === action.payload.id) {
          state.activeOrder = action.payload;
        }
      });

    // Cancel order
    builder
      .addCase(cancelOrder.fulfilled, (state, action) => {
        const orderIndex = state.orders.findIndex(order => order.id === action.payload.id);
        if (orderIndex !== -1) {
          state.orders[orderIndex] = action.payload;
        }
        if (state.activeOrder && state.activeOrder.id === action.payload.id) {
          state.activeOrder = action.payload;
        }
      });

    // Track order
    builder
      .addCase(trackOrder.fulfilled, (state, action) => {
        const orderIndex = state.orders.findIndex(order => order.id === action.payload.id);
        if (orderIndex !== -1) {
          state.orders[orderIndex].tracking = action.payload.tracking;
        }
        if (state.activeOrder && state.activeOrder.id === action.payload.id) {
          state.activeOrder.tracking = action.payload.tracking;
        }
      });

    // Rate order
    builder
      .addCase(rateOrder.fulfilled, (state, action) => {
        const orderIndex = state.orders.findIndex(order => order.id === action.payload.id);
        if (orderIndex !== -1) {
          state.orders[orderIndex] = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setActiveOrder,
  updateOrderStatus,
  clearActiveOrder,
} = orderSlice.actions;

export default orderSlice.reducer;
