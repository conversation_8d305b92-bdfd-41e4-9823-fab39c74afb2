import {createSlice, PayloadAction} from '@reduxjs/toolkit';

export interface CartItem {
  id: string;
  menuItemId: string;
  restaurantId: string;
  name: string;
  price: number;
  quantity: number;
  customizations: CartCustomization[];
  specialInstructions?: string;
  image?: string;
}

export interface CartCustomization {
  id: string;
  name: string;
  options: string[];
  additionalPrice: number;
}

export interface Cart {
  restaurantId: string | null;
  restaurantName: string | null;
  items: CartItem[];
  subtotal: number;
  deliveryFee: number;
  tax: number;
  discount: number;
  total: number;
  promoCode?: string;
  minimumOrderMet: boolean;
  minimumOrderAmount: number;
}

interface CartState extends Cart {
  isLoading: boolean;
  error: string | null;
}

const initialState: CartState = {
  restaurantId: null,
  restaurantName: null,
  items: [],
  subtotal: 0,
  deliveryFee: 0,
  tax: 0,
  discount: 0,
  total: 0,
  promoCode: undefined,
  minimumOrderMet: false,
  minimumOrderAmount: 0,
  isLoading: false,
  error: null,
};

const calculateCartTotals = (state: CartState) => {
  // Calculate subtotal
  state.subtotal = state.items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const customizationTotal = item.customizations.reduce(
      (custTotal, customization) => custTotal + customization.additionalPrice * item.quantity,
      0
    );
    return total + itemTotal + customizationTotal;
  }, 0);

  // Calculate tax (assuming 8% tax rate)
  state.tax = state.subtotal * 0.08;

  // Calculate total
  state.total = state.subtotal + state.deliveryFee + state.tax - state.discount;

  // Check minimum order
  state.minimumOrderMet = state.subtotal >= state.minimumOrderAmount;
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<{
      item: Omit<CartItem, 'id'>;
      restaurantName: string;
      minimumOrderAmount: number;
      deliveryFee: number;
    }>) => {
      const {item, restaurantName, minimumOrderAmount, deliveryFee} = action.payload;

      // If cart is empty or from different restaurant, clear it
      if (!state.restaurantId || state.restaurantId !== item.restaurantId) {
        state.items = [];
        state.restaurantId = item.restaurantId;
        state.restaurantName = restaurantName;
        state.minimumOrderAmount = minimumOrderAmount;
        state.deliveryFee = deliveryFee;
      }

      // Check if item with same customizations already exists
      const existingItemIndex = state.items.findIndex(cartItem => 
        cartItem.menuItemId === item.menuItemId &&
        JSON.stringify(cartItem.customizations) === JSON.stringify(item.customizations) &&
        cartItem.specialInstructions === item.specialInstructions
      );

      if (existingItemIndex !== -1) {
        // Update quantity of existing item
        state.items[existingItemIndex].quantity += item.quantity;
      } else {
        // Add new item
        const newItem: CartItem = {
          ...item,
          id: `${item.menuItemId}_${Date.now()}_${Math.random()}`,
        };
        state.items.push(newItem);
      }

      calculateCartTotals(state);
    },

    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      
      // Clear cart if no items left
      if (state.items.length === 0) {
        state.restaurantId = null;
        state.restaurantName = null;
        state.minimumOrderAmount = 0;
        state.deliveryFee = 0;
        state.promoCode = undefined;
        state.discount = 0;
      }

      calculateCartTotals(state);
    },

    updateItemQuantity: (state, action: PayloadAction<{itemId: string; quantity: number}>) => {
      const {itemId, quantity} = action.payload;
      const itemIndex = state.items.findIndex(item => item.id === itemId);

      if (itemIndex !== -1) {
        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          state.items.splice(itemIndex, 1);
        } else {
          state.items[itemIndex].quantity = quantity;
        }
      }

      // Clear cart if no items left
      if (state.items.length === 0) {
        state.restaurantId = null;
        state.restaurantName = null;
        state.minimumOrderAmount = 0;
        state.deliveryFee = 0;
        state.promoCode = undefined;
        state.discount = 0;
      }

      calculateCartTotals(state);
    },

    updateItemCustomizations: (state, action: PayloadAction<{
      itemId: string;
      customizations: CartCustomization[];
    }>) => {
      const {itemId, customizations} = action.payload;
      const itemIndex = state.items.findIndex(item => item.id === itemId);

      if (itemIndex !== -1) {
        state.items[itemIndex].customizations = customizations;
      }

      calculateCartTotals(state);
    },

    updateItemInstructions: (state, action: PayloadAction<{
      itemId: string;
      instructions: string;
    }>) => {
      const {itemId, instructions} = action.payload;
      const itemIndex = state.items.findIndex(item => item.id === itemId);

      if (itemIndex !== -1) {
        state.items[itemIndex].specialInstructions = instructions;
      }
    },

    applyPromoCode: (state, action: PayloadAction<{code: string; discount: number}>) => {
      const {code, discount} = action.payload;
      state.promoCode = code;
      state.discount = discount;
      calculateCartTotals(state);
    },

    removePromoCode: (state) => {
      state.promoCode = undefined;
      state.discount = 0;
      calculateCartTotals(state);
    },

    clearCart: (state) => {
      state.restaurantId = null;
      state.restaurantName = null;
      state.items = [];
      state.subtotal = 0;
      state.deliveryFee = 0;
      state.tax = 0;
      state.discount = 0;
      state.total = 0;
      state.promoCode = undefined;
      state.minimumOrderMet = false;
      state.minimumOrderAmount = 0;
      state.error = null;
    },

    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const {
  addToCart,
  removeFromCart,
  updateItemQuantity,
  updateItemCustomizations,
  updateItemInstructions,
  applyPromoCode,
  removePromoCode,
  clearCart,
  setError,
  clearError,
  setLoading,
} = cartSlice.actions;

export default cartSlice.reducer;
