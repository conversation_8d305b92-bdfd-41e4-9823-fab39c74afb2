import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  data?: any;
  isRead: boolean;
  createdAt: string;
  expiresAt?: string;
}

export type NotificationType = 
  | 'order_confirmed'
  | 'order_preparing'
  | 'order_ready'
  | 'order_picked_up'
  | 'order_delivered'
  | 'order_cancelled'
  | 'promotion'
  | 'new_restaurant'
  | 'delivery_assigned'
  | 'payment_success'
  | 'payment_failed'
  | 'general';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  pushToken: string | null;
  preferences: NotificationPreferences;
}

export interface NotificationPreferences {
  orderUpdates: boolean;
  promotions: boolean;
  newRestaurants: boolean;
  deliveryUpdates: boolean;
  paymentAlerts: boolean;
  sound: boolean;
  vibration: boolean;
}

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  pushToken: null,
  preferences: {
    orderUpdates: true,
    promotions: true,
    newRestaurants: false,
    deliveryUpdates: true,
    paymentAlerts: true,
    sound: true,
    vibration: true,
  },
};

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notification/fetchNotifications',
  async (_, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/notifications');
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notification/markAsRead',
  async (notificationId: string, {rejectWithValue}) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notification/markAllAsRead',
  async (_, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'PUT',
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteNotification = createAsyncThunk(
  'notification/deleteNotification',
  async (notificationId: string, {rejectWithValue}) => {
    try {
      await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      });
      return notificationId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updatePushToken = createAsyncThunk(
  'notification/updatePushToken',
  async (token: string, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/notifications/push-token', {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({token}),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateNotificationPreferences = createAsyncThunk(
  'notification/updatePreferences',
  async (preferences: Partial<NotificationPreferences>, {rejectWithValue}) => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(preferences),
      });
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.unshift(action.payload);
      if (!action.payload.isRead) {
        state.unreadCount += 1;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    setPushToken: (state, action: PayloadAction<string>) => {
      state.pushToken = action.payload;
    },
    updateLocalPreferences: (state, action: PayloadAction<Partial<NotificationPreferences>>) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      };
    },
    clearNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
    },
    removeExpiredNotifications: (state) => {
      const now = new Date().toISOString();
      const validNotifications = state.notifications.filter(
        notification => !notification.expiresAt || notification.expiresAt > now
      );
      
      const removedUnreadCount = state.notifications
        .filter(notification => 
          notification.expiresAt && 
          notification.expiresAt <= now && 
          !notification.isRead
        ).length;
      
      state.notifications = validNotifications;
      state.unreadCount = Math.max(0, state.unreadCount - removedUnreadCount);
    },
  },
  extraReducers: (builder) => {
    // Fetch notifications
    builder
      .addCase(fetchNotifications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.notifications = action.payload.notifications;
        state.unreadCount = action.payload.unreadCount;
        state.preferences = action.payload.preferences || state.preferences;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Mark as read
    builder
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notificationIndex = state.notifications.findIndex(
          notification => notification.id === action.payload.id
        );
        if (notificationIndex !== -1 && !state.notifications[notificationIndex].isRead) {
          state.notifications[notificationIndex].isRead = true;
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      });

    // Mark all as read
    builder
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.notifications = state.notifications.map(notification => ({
          ...notification,
          isRead: true,
        }));
        state.unreadCount = 0;
      });

    // Delete notification
    builder
      .addCase(deleteNotification.fulfilled, (state, action) => {
        const notificationIndex = state.notifications.findIndex(
          notification => notification.id === action.payload
        );
        if (notificationIndex !== -1) {
          const wasUnread = !state.notifications[notificationIndex].isRead;
          state.notifications.splice(notificationIndex, 1);
          if (wasUnread) {
            state.unreadCount = Math.max(0, state.unreadCount - 1);
          }
        }
      });

    // Update push token
    builder
      .addCase(updatePushToken.fulfilled, (state, action) => {
        state.pushToken = action.payload.token;
      });

    // Update notification preferences
    builder
      .addCase(updateNotificationPreferences.fulfilled, (state, action) => {
        state.preferences = action.payload;
      });
  },
});

export const {
  addNotification,
  clearError,
  setPushToken,
  updateLocalPreferences,
  clearNotifications,
  removeExpiredNotifications,
} = notificationSlice.actions;

export default notificationSlice.reducer;
