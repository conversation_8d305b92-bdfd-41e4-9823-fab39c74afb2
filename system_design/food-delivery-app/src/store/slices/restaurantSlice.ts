import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';

export interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisine: string[];
  rating: number;
  reviewCount: number;
  deliveryTime: string;
  deliveryFee: number;
  minimumOrder: number;
  image: string;
  images: string[];
  address: string;
  latitude: number;
  longitude: number;
  isOpen: boolean;
  openingHours: OpeningHours;
  menu: MenuItem[];
  tags: string[];
  featured: boolean;
  distance?: number;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category: string;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  isSpicy: boolean;
  isAvailable: boolean;
  customizations: Customization[];
  nutritionInfo?: NutritionInfo;
}

export interface Customization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
}

export interface OpeningHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

export interface DayHours {
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sodium: number;
}

interface RestaurantState {
  nearbyRestaurants: Restaurant[];
  featuredRestaurants: Restaurant[];
  searchResults: Restaurant[];
  selectedRestaurant: Restaurant | null;
  categories: string[];
  filters: RestaurantFilters;
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
}

export interface RestaurantFilters {
  cuisine: string[];
  priceRange: [number, number];
  rating: number;
  deliveryTime: number;
  isVegetarian: boolean;
  isVegan: boolean;
  sortBy: 'rating' | 'deliveryTime' | 'deliveryFee' | 'distance';
}

const initialState: RestaurantState = {
  nearbyRestaurants: [],
  featuredRestaurants: [],
  searchResults: [],
  selectedRestaurant: null,
  categories: [],
  filters: {
    cuisine: [],
    priceRange: [0, 100],
    rating: 0,
    deliveryTime: 60,
    isVegetarian: false,
    isVegan: false,
    sortBy: 'rating',
  },
  isLoading: false,
  isSearching: false,
  error: null,
};

// Async thunks
export const fetchNearbyRestaurants = createAsyncThunk(
  'restaurant/fetchNearby',
  async (location: {latitude: number; longitude: number}, {rejectWithValue}) => {
    try {
      // API call would go here
      const response = await fetch(`/api/restaurants/nearby?lat=${location.latitude}&lng=${location.longitude}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchFeaturedRestaurants = createAsyncThunk(
  'restaurant/fetchFeatured',
  async (_, {rejectWithValue}) => {
    try {
      // API call would go here
      const response = await fetch('/api/restaurants/featured');
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const searchRestaurants = createAsyncThunk(
  'restaurant/search',
  async (searchParams: {
    query?: string;
    location: {latitude: number; longitude: number};
    filters?: Partial<RestaurantFilters>;
  }, {rejectWithValue}) => {
    try {
      const queryParams = new URLSearchParams({
        q: searchParams.query || '',
        lat: searchParams.location.latitude.toString(),
        lng: searchParams.location.longitude.toString(),
        ...searchParams.filters,
      });
      
      const response = await fetch(`/api/restaurants/search?${queryParams}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchRestaurantDetails = createAsyncThunk(
  'restaurant/fetchDetails',
  async (restaurantId: string, {rejectWithValue}) => {
    try {
      // API call would go here
      const response = await fetch(`/api/restaurants/${restaurantId}`);
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'restaurant/fetchCategories',
  async (_, {rejectWithValue}) => {
    try {
      // API call would go here
      const response = await fetch('/api/restaurants/categories');
      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const restaurantSlice = createSlice({
  name: 'restaurant',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedRestaurant: (state, action: PayloadAction<Restaurant | null>) => {
      state.selectedRestaurant = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<RestaurantFilters>>) => {
      state.filters = {...state.filters, ...action.payload};
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    clearSelectedRestaurant: (state) => {
      state.selectedRestaurant = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch nearby restaurants
    builder
      .addCase(fetchNearbyRestaurants.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNearbyRestaurants.fulfilled, (state, action) => {
        state.isLoading = false;
        state.nearbyRestaurants = action.payload;
      })
      .addCase(fetchNearbyRestaurants.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch featured restaurants
    builder
      .addCase(fetchFeaturedRestaurants.fulfilled, (state, action) => {
        state.featuredRestaurants = action.payload;
      });

    // Search restaurants
    builder
      .addCase(searchRestaurants.pending, (state) => {
        state.isSearching = true;
        state.error = null;
      })
      .addCase(searchRestaurants.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload;
      })
      .addCase(searchRestaurants.rejected, (state, action) => {
        state.isSearching = false;
        state.error = action.payload as string;
      });

    // Fetch restaurant details
    builder
      .addCase(fetchRestaurantDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchRestaurantDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedRestaurant = action.payload;
      })
      .addCase(fetchRestaurantDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch categories
    builder
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      });
  },
});

export const {
  clearError,
  setSelectedRestaurant,
  updateFilters,
  clearSearchResults,
  clearSelectedRestaurant,
} = restaurantSlice.actions;

export default restaurantSlice.reducer;
