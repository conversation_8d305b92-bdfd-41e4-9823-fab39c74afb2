import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {useSelector} from 'react-redux';
import {RootState} from '../store';

// Auth Screens
import WelcomeScreen from '../screens/auth/WelcomeScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import OTPVerificationScreen from '../screens/auth/OTPVerificationScreen';

// Customer Navigation
import CustomerTabNavigator from './CustomerTabNavigator';

// Restaurant Navigation
import RestaurantTabNavigator from './RestaurantTabNavigator';

// Delivery Navigation
import DeliveryTabNavigator from './DeliveryTabNavigator';

export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  OTPVerification: {phoneNumber: string};
  CustomerApp: undefined;
  RestaurantApp: undefined;
  DeliveryApp: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const {isAuthenticated, userType} = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
        }}>
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Register" component={RegisterScreen} />
        <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />
      </Stack.Navigator>
    );
  }

  // Navigate to appropriate app based on user type
  const getMainNavigator = () => {
    switch (userType) {
      case 'customer':
        return <Stack.Screen name="CustomerApp" component={CustomerTabNavigator} />;
      case 'restaurant':
        return <Stack.Screen name="RestaurantApp" component={RestaurantTabNavigator} />;
      case 'delivery':
        return <Stack.Screen name="DeliveryApp" component={DeliveryTabNavigator} />;
      default:
        return <Stack.Screen name="CustomerApp" component={CustomerTabNavigator} />;
    }
  };

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      {getMainNavigator()}
    </Stack.Navigator>
  );
};

export default AppNavigator;
