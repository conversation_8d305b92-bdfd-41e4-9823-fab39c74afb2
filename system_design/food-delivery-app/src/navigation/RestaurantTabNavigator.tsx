import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Restaurant Screens
import RestaurantDashboardScreen from '../screens/restaurant/RestaurantDashboardScreen';
import OrderManagementScreen from '../screens/restaurant/OrderManagementScreen';
import MenuManagementScreen from '../screens/restaurant/MenuManagementScreen';
import RestaurantAnalyticsScreen from '../screens/restaurant/RestaurantAnalyticsScreen';
import RestaurantProfileScreen from '../screens/restaurant/RestaurantProfileScreen';

export type RestaurantTabParamList = {
  Dashboard: undefined;
  Orders: undefined;
  Menu: undefined;
  Analytics: undefined;
  Profile: undefined;
};

export type RestaurantStackParamList = {
  RestaurantTabs: undefined;
  OrderDetails: {orderId: string};
  MenuItemEdit: {itemId?: string};
  RestaurantSettings: undefined;
};

const Tab = createBottomTabNavigator<RestaurantTabParamList>();
const Stack = createStackNavigator<RestaurantStackParamList>();

const RestaurantTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Orders':
              iconName = 'receipt-long';
              break;
            case 'Menu':
              iconName = 'restaurant-menu';
              break;
            case 'Analytics':
              iconName = 'analytics';
              break;
            case 'Profile':
              iconName = 'store';
              break;
            default:
              iconName = 'dashboard';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
      })}>
      <Tab.Screen 
        name="Dashboard" 
        component={RestaurantDashboardScreen}
        options={{tabBarLabel: 'Dashboard'}}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrderManagementScreen}
        options={{tabBarLabel: 'Orders'}}
      />
      <Tab.Screen 
        name="Menu" 
        component={MenuManagementScreen}
        options={{tabBarLabel: 'Menu'}}
      />
      <Tab.Screen 
        name="Analytics" 
        component={RestaurantAnalyticsScreen}
        options={{tabBarLabel: 'Analytics'}}
      />
      <Tab.Screen 
        name="Profile" 
        component={RestaurantProfileScreen}
        options={{tabBarLabel: 'Profile'}}
      />
    </Tab.Navigator>
  );
};

const RestaurantTabNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      <Stack.Screen name="RestaurantTabs" component={RestaurantTabs} />
    </Stack.Navigator>
  );
};

export default RestaurantTabNavigator;
