import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Customer Screens
import HomeScreen from '../screens/customer/HomeScreen';
import SearchScreen from '../screens/customer/SearchScreen';
import OrdersScreen from '../screens/customer/OrdersScreen';
import ProfileScreen from '../screens/customer/ProfileScreen';
import RestaurantDetailScreen from '../screens/customer/RestaurantDetailScreen';
import MenuItemScreen from '../screens/customer/MenuItemScreen';
import CartScreen from '../screens/customer/CartScreen';
import CheckoutScreen from '../screens/customer/CheckoutScreen';
import OrderTrackingScreen from '../screens/customer/OrderTrackingScreen';
import PaymentScreen from '../screens/customer/PaymentScreen';

export type CustomerTabParamList = {
  Home: undefined;
  Search: undefined;
  Orders: undefined;
  Profile: undefined;
};

export type CustomerStackParamList = {
  CustomerTabs: undefined;
  RestaurantDetail: {restaurantId: string};
  MenuItem: {itemId: string; restaurantId: string};
  Cart: undefined;
  Checkout: undefined;
  Payment: {orderId: string; amount: number};
  OrderTracking: {orderId: string};
};

const Tab = createBottomTabNavigator<CustomerTabParamList>();
const Stack = createStackNavigator<CustomerStackParamList>();

const CustomerTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Search':
              iconName = 'search';
              break;
            case 'Orders':
              iconName = 'receipt';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'home';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
      })}>
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{tabBarLabel: 'Home'}}
      />
      <Tab.Screen 
        name="Search" 
        component={SearchScreen}
        options={{tabBarLabel: 'Search'}}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{tabBarLabel: 'Orders'}}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{tabBarLabel: 'Profile'}}
      />
    </Tab.Navigator>
  );
};

const CustomerTabNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      <Stack.Screen name="CustomerTabs" component={CustomerTabs} />
      <Stack.Screen 
        name="RestaurantDetail" 
        component={RestaurantDetailScreen}
        options={{
          headerShown: true,
          headerTitle: 'Restaurant',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="MenuItem" 
        component={MenuItemScreen}
        options={{
          headerShown: true,
          headerTitle: 'Menu Item',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="Cart" 
        component={CartScreen}
        options={{
          headerShown: true,
          headerTitle: 'Cart',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="Checkout" 
        component={CheckoutScreen}
        options={{
          headerShown: true,
          headerTitle: 'Checkout',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="Payment" 
        component={PaymentScreen}
        options={{
          headerShown: true,
          headerTitle: 'Payment',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="OrderTracking" 
        component={OrderTrackingScreen}
        options={{
          headerShown: true,
          headerTitle: 'Track Order',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default CustomerTabNavigator;
