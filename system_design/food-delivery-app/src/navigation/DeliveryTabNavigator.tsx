import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Delivery Screens
import DeliveryDashboardScreen from '../screens/delivery/DeliveryDashboardScreen';
import AvailableOrdersScreen from '../screens/delivery/AvailableOrdersScreen';
import ActiveDeliveryScreen from '../screens/delivery/ActiveDeliveryScreen';
import DeliveryEarningsScreen from '../screens/delivery/DeliveryEarningsScreen';
import DeliveryProfileScreen from '../screens/delivery/DeliveryProfileScreen';

export type DeliveryTabParamList = {
  Dashboard: undefined;
  Available: undefined;
  Active: undefined;
  Earnings: undefined;
  Profile: undefined;
};

export type DeliveryStackParamList = {
  DeliveryTabs: undefined;
  OrderNavigation: {orderId: string};
  DeliveryHistory: undefined;
  DeliverySettings: undefined;
};

const Tab = createBottomTabNavigator<DeliveryTabParamList>();
const Stack = createStackNavigator<DeliveryStackParamList>();

const DeliveryTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Available':
              iconName = 'local-shipping';
              break;
            case 'Active':
              iconName = 'navigation';
              break;
            case 'Earnings':
              iconName = 'account-balance-wallet';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'dashboard';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
      })}>
      <Tab.Screen 
        name="Dashboard" 
        component={DeliveryDashboardScreen}
        options={{tabBarLabel: 'Dashboard'}}
      />
      <Tab.Screen 
        name="Available" 
        component={AvailableOrdersScreen}
        options={{tabBarLabel: 'Available'}}
      />
      <Tab.Screen 
        name="Active" 
        component={ActiveDeliveryScreen}
        options={{tabBarLabel: 'Active'}}
      />
      <Tab.Screen 
        name="Earnings" 
        component={DeliveryEarningsScreen}
        options={{tabBarLabel: 'Earnings'}}
      />
      <Tab.Screen 
        name="Profile" 
        component={DeliveryProfileScreen}
        options={{tabBarLabel: 'Profile'}}
      />
    </Tab.Navigator>
  );
};

const DeliveryTabNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      <Stack.Screen name="DeliveryTabs" component={DeliveryTabs} />
    </Stack.Navigator>
  );
};

export default DeliveryTabNavigator;
