import messaging from '@react-native-firebase/messaging';
import {Platform, PermissionsAndroid, Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {store} from '../store';
import {addNotification, setPushToken} from '../store/slices/notificationSlice';

class NotificationService {
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;

    try {
      await this.requestPermission();
      await this.getToken();
      this.setupMessageHandlers();
      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  async requestPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
        return true; // Android < 13 doesn't need runtime permission
      }

      // iOS
      const authStatus = await messaging().requestPermission({
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        provisional: false,
        sound: true,
      });

      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        Alert.alert(
          'Notifications Disabled',
          'Please enable notifications in your device settings to receive order updates.',
          [
            {text: 'Cancel', style: 'cancel'},
            {text: 'Settings', onPress: () => this.openSettings()},
          ]
        );
      }

      return enabled;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  async getToken(): Promise<string | null> {
    try {
      const token = await messaging().getToken();
      if (token) {
        await AsyncStorage.setItem('fcmToken', token);
        store.dispatch(setPushToken(token));
        
        // Send token to backend
        await this.sendTokenToBackend(token);
      }
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  private async sendTokenToBackend(token: string) {
    try {
      // This would be your API call to save the token
      await fetch('/api/notifications/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authorization header if needed
        },
        body: JSON.stringify({token}),
      });
    } catch (error) {
      console.error('Error sending token to backend:', error);
    }
  }

  setupMessageHandlers() {
    // Handle background messages
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Message handled in the background!', remoteMessage);
      this.handleNotification(remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async (remoteMessage) => {
      console.log('Message handled in the foreground!', remoteMessage);
      this.handleNotification(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification caused app to open from background state:', remoteMessage);
      this.handleNotificationTap(remoteMessage);
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);
          this.handleNotificationTap(remoteMessage);
        }
      });

    // Handle token refresh
    messaging().onTokenRefresh((token) => {
      console.log('FCM token refreshed:', token);
      this.sendTokenToBackend(token);
      store.dispatch(setPushToken(token));
    });
  }

  private handleNotification(remoteMessage: any) {
    const notification = {
      id: remoteMessage.messageId || Date.now().toString(),
      title: remoteMessage.notification?.title || 'New Notification',
      message: remoteMessage.notification?.body || '',
      type: remoteMessage.data?.type || 'general',
      data: remoteMessage.data,
      isRead: false,
      createdAt: new Date().toISOString(),
    };

    store.dispatch(addNotification(notification));
  }

  private handleNotificationTap(remoteMessage: any) {
    // Handle navigation based on notification type
    const notificationType = remoteMessage.data?.type;
    const orderId = remoteMessage.data?.orderId;

    switch (notificationType) {
      case 'order_confirmed':
      case 'order_preparing':
      case 'order_ready':
      case 'order_picked_up':
      case 'order_delivered':
        if (orderId) {
          // Navigate to order tracking screen
          // This would be handled by your navigation service
          console.log('Navigate to order tracking:', orderId);
        }
        break;
      case 'promotion':
        // Navigate to promotions screen
        console.log('Navigate to promotions');
        break;
      default:
        // Navigate to notifications screen
        console.log('Navigate to notifications');
        break;
    }
  }

  async scheduleLocalNotification(notification: {
    title: string;
    body: string;
    data?: any;
    delay?: number; // in milliseconds
  }) {
    // This would use a local notification library like @react-native-async-storage/async-storage
    // For now, we'll just add it to the store
    const localNotification = {
      id: Date.now().toString(),
      title: notification.title,
      message: notification.body,
      type: 'general' as const,
      data: notification.data,
      isRead: false,
      createdAt: new Date().toISOString(),
    };

    if (notification.delay) {
      setTimeout(() => {
        store.dispatch(addNotification(localNotification));
      }, notification.delay);
    } else {
      store.dispatch(addNotification(localNotification));
    }
  }

  async clearAllNotifications() {
    try {
      // Clear badge count
      if (Platform.OS === 'ios') {
        await messaging().setApplicationIconBadgeNumber(0);
      }
      
      // Clear notification center
      // This would require additional native code or a library
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  private openSettings() {
    // This would open the app settings
    // You'd need to use a library like react-native-permissions for this
    console.log('Open app settings');
  }
}

export const notificationService = new NotificationService();

// Convenience functions for backward compatibility
export const requestUserPermission = () => notificationService.requestPermission();
export const notificationListener = () => notificationService.initialize();
