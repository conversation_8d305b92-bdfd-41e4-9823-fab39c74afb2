import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {API_BASE_URL} from '../config/constants';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  phone: string;
  password: string;
  userType: 'customer' | 'restaurant' | 'delivery';
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    phone: string;
    name: string;
    userType: 'customer' | 'restaurant' | 'delivery';
    profileImage?: string;
    isVerified: boolean;
  };
  token: string;
}

class AuthService {
  private baseURL = `${API_BASE_URL}/auth`;

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/login`, credentials);
      const {user, token} = response.data;
      
      // Store token in AsyncStorage
      await AsyncStorage.setItem('authToken', token);
      
      return {user, token};
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${this.baseURL}/register`, userData);
      const {user, token} = response.data;
      
      // Store token in AsyncStorage
      await AsyncStorage.setItem('authToken', token);
      
      return {user, token};
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  }

  async verifyOTP(data: {phone: string; otp: string}): Promise<{success: boolean}> {
    try {
      const response = await axios.post(`${this.baseURL}/verify-otp`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'OTP verification failed');
    }
  }

  async sendOTP(phone: string): Promise<{success: boolean}> {
    try {
      const response = await axios.post(`${this.baseURL}/send-otp`, {phone});
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send OTP');
    }
  }

  async logout(): Promise<void> {
    try {
      // Remove token from AsyncStorage
      await AsyncStorage.removeItem('authToken');
      
      // Optional: Call logout endpoint to invalidate token on server
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        await axios.post(`${this.baseURL}/logout`, {}, {
          headers: {Authorization: `Bearer ${token}`}
        });
      }
    } catch (error) {
      // Even if server logout fails, we still remove local token
      console.error('Logout error:', error);
    }
  }

  async refreshToken(): Promise<string> {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await axios.post(`${this.baseURL}/refresh-token`, {}, {
        headers: {Authorization: `Bearer ${token}`}
      });
      
      const newToken = response.data.token;
      await AsyncStorage.setItem('authToken', newToken);
      
      return newToken;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Token refresh failed');
    }
  }

  async forgotPassword(email: string): Promise<{success: boolean}> {
    try {
      const response = await axios.post(`${this.baseURL}/forgot-password`, {email});
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to send reset email');
    }
  }

  async resetPassword(data: {
    email: string;
    token: string;
    newPassword: string;
  }): Promise<{success: boolean}> {
    try {
      const response = await axios.post(`${this.baseURL}/reset-password`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Password reset failed');
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('authToken');
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  async validateToken(token: string): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/validate-token`, {
        headers: {Authorization: `Bearer ${token}`}
      });
      return response.data.valid;
    } catch (error) {
      return false;
    }
  }
}

export const authService = new AuthService();
