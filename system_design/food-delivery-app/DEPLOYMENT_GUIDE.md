# Food Delivery App - Deployment Guide

This guide covers the complete process of building, testing, and deploying your food delivery app to the Google Play Store and Apple App Store.

## Prerequisites

### Development Environment Setup

1. **Node.js and npm**
   ```bash
   # Install Node.js (v16 or higher)
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 18
   nvm use 18
   ```

2. **React Native CLI**
   ```bash
   npm install -g react-native-cli
   npm install -g @react-native-community/cli
   ```

3. **Android Development**
   - Download and install [Android Studio](https://developer.android.com/studio)
   - Install Android SDK (API level 33 or higher)
   - Set up Android emulator or connect physical device
   - Configure environment variables:
     ```bash
     export ANDROID_HOME=$HOME/Library/Android/sdk
     export PATH=$PATH:$ANDROID_HOME/emulator
     export PATH=$PATH:$ANDROID_HOME/tools
     export PATH=$PATH:$ANDROID_HOME/tools/bin
     export PATH=$PATH:$ANDROID_HOME/platform-tools
     ```

4. **iOS Development (macOS only)**
   - Install Xcode from Mac App Store
   - Install Xcode Command Line Tools: `xcode-select --install`
   - Install CocoaPods: `sudo gem install cocoapods`

## Project Setup

### 1. Clone and Install Dependencies

```bash
# Navigate to your project directory
cd food-delivery-app

# Install Node.js dependencies
npm install

# Install iOS dependencies (macOS only)
cd ios && pod install && cd ..
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```env
# API Configuration
API_BASE_URL=https://your-api-domain.com/api

# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_firebase_app_id

# Payment Gateways
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
RAZORPAY_KEY_ID=rzp_live_your_razorpay_key

# SMS Service
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token

# App Configuration
APP_VERSION=1.0.0
BUILD_NUMBER=1
```

## Testing

### 1. Unit Testing
```bash
npm test
```

### 2. Integration Testing
```bash
npm run test:integration
```

### 3. E2E Testing (Detox)
```bash
# iOS
npm run test:e2e:ios

# Android
npm run test:e2e:android
```

### 4. Manual Testing Checklist

- [ ] User registration and login
- [ ] Restaurant browsing and search
- [ ] Order placement and payment
- [ ] Real-time order tracking
- [ ] Push notifications
- [ ] Location services
- [ ] Camera permissions (profile photos)
- [ ] Offline functionality
- [ ] Performance on low-end devices

## Building for Production

### Android APK/AAB Generation

#### 1. Generate Signing Key
```bash
cd android/app
keytool -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

#### 2. Configure Gradle Properties
Create `android/gradle.properties`:
```properties
MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=my-key-alias
MYAPP_UPLOAD_STORE_PASSWORD=your_store_password
MYAPP_UPLOAD_KEY_PASSWORD=your_key_password
```

#### 3. Build Release APK
```bash
cd android
./gradlew assembleRelease
```

#### 4. Build Release AAB (Recommended for Play Store)
```bash
cd android
./gradlew bundleRelease
```

The generated files will be in:
- APK: `android/app/build/outputs/apk/release/app-release.apk`
- AAB: `android/app/build/outputs/bundle/release/app-release.aab`

### iOS Archive Generation (macOS only)

#### 1. Open Xcode Project
```bash
cd ios
open FoodDeliveryApp.xcworkspace
```

#### 2. Configure Signing & Capabilities
- Select your development team
- Configure bundle identifier
- Enable required capabilities (Push Notifications, Background Modes, etc.)

#### 3. Archive for Distribution
- Select "Any iOS Device" as target
- Product → Archive
- Upload to App Store Connect or export for distribution

## Google Play Store Deployment

### 1. Play Console Setup

1. **Create Developer Account**
   - Visit [Google Play Console](https://play.google.com/console)
   - Pay one-time $25 registration fee
   - Complete account verification

2. **Create New App**
   - Click "Create app"
   - Fill in app details:
     - App name: "Food Delivery"
     - Default language: English (US)
     - App or game: App
     - Free or paid: Free (or Paid)

### 2. App Content and Policies

#### Store Listing
- **App name**: Food Delivery
- **Short description**: Order food from local restaurants
- **Full description**: 
  ```
  Discover and order from the best local restaurants in your area. 
  Our food delivery app connects you with hundreds of restaurants, 
  offering real-time tracking, secure payments, and fast delivery.

  Features:
  • Browse restaurants by cuisine, rating, and delivery time
  • Real-time order tracking with GPS
  • Secure payment with multiple options
  • Order history and favorites
  • Push notifications for order updates
  • Customer support chat
  ```

#### Graphics Assets Required
- **App icon**: 512 x 512 px (PNG, no transparency)
- **Feature graphic**: 1024 x 500 px
- **Phone screenshots**: At least 2, up to 8 (16:9 or 9:16 aspect ratio)
- **Tablet screenshots**: At least 1 (if supporting tablets)

#### Content Rating
- Complete the content rating questionnaire
- Typical rating for food delivery apps: "Everyone" or "Teen"

#### Privacy Policy
- Required for all apps
- Must be accessible via URL
- Should cover data collection, usage, and sharing

### 3. Release Management

#### Internal Testing
1. Upload AAB file
2. Add internal testers (email addresses)
3. Test thoroughly before proceeding

#### Closed Testing (Alpha/Beta)
1. Create closed testing track
2. Upload AAB file
3. Add test users or use opt-in URL
4. Gather feedback and fix issues

#### Production Release
1. Upload final AAB file
2. Complete all required sections
3. Submit for review
4. Review typically takes 1-3 days

### 4. App Bundle Optimization

Enable Play App Signing for:
- Automatic APK optimization
- Smaller download sizes
- Enhanced security

## Apple App Store Deployment

### 1. App Store Connect Setup

1. **Apple Developer Account**
   - Enroll in Apple Developer Program ($99/year)
   - Complete verification process

2. **Create App Record**
   - Visit [App Store Connect](https://appstoreconnect.apple.com)
   - Create new app with unique bundle ID

### 2. App Information

#### Metadata
- **App name**: Food Delivery
- **Subtitle**: Order food from local restaurants
- **Category**: Food & Drink
- **Content rating**: 4+ (typical for food apps)

#### App Description
```
Discover and order from the best local restaurants in your area. 
Our food delivery app connects you with hundreds of restaurants, 
offering real-time tracking, secure payments, and fast delivery.

What's New:
• Browse restaurants by cuisine, rating, and delivery time
• Real-time order tracking with GPS
• Secure payment with multiple options
• Order history and favorites
• Push notifications for order updates
• 24/7 customer support

Download now and get your favorite food delivered to your door!
```

#### Screenshots and Media
- **iPhone screenshots**: 6.5" and 5.5" display sizes
- **iPad screenshots**: 12.9" and 2nd gen 12.9" (if supporting iPad)
- **App preview videos**: Optional but recommended

### 3. Build Upload

#### Using Xcode
1. Archive your app (Product → Archive)
2. Select "Distribute App"
3. Choose "App Store Connect"
4. Upload and wait for processing

#### Using Application Loader
1. Export IPA from Xcode
2. Use Application Loader to upload

### 4. TestFlight Beta Testing

1. Add internal testers (up to 100)
2. Add external testers (up to 10,000)
3. Collect feedback and crash reports
4. Iterate and improve

### 5. App Review Submission

1. Complete all metadata
2. Add build to release
3. Submit for review
4. Review typically takes 24-48 hours

## Post-Launch Monitoring

### Analytics and Crash Reporting

1. **Firebase Analytics**
   - User engagement metrics
   - Conversion tracking
   - Custom events

2. **Crashlytics**
   - Real-time crash reporting
   - Performance monitoring
   - User impact analysis

3. **App Store Analytics**
   - Download metrics
   - User ratings and reviews
   - Search performance

### Performance Monitoring

- Monitor app performance metrics
- Track API response times
- Monitor user retention rates
- Analyze user feedback

### Updates and Maintenance

1. **Regular Updates**
   - Bug fixes and improvements
   - New features based on user feedback
   - Security updates

2. **Version Management**
   - Semantic versioning (1.0.0, 1.0.1, 1.1.0)
   - Maintain backward compatibility
   - Gradual rollout for major changes

## Troubleshooting Common Issues

### Build Issues
- Clear Metro cache: `npx react-native start --reset-cache`
- Clean builds: `cd android && ./gradlew clean`
- Reinstall dependencies: `rm -rf node_modules && npm install`

### Play Store Rejections
- Policy violations (check Play Console policies)
- Missing privacy policy
- Incomplete store listing
- Technical issues (crashes, ANRs)

### App Store Rejections
- Guideline violations (check App Store Review Guidelines)
- Missing metadata or screenshots
- App crashes or doesn't function as described
- Privacy concerns

## Support and Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Google Play Console Help](https://support.google.com/googleplay/android-developer)
- [App Store Connect Help](https://developer.apple.com/support/app-store-connect/)
- [Firebase Documentation](https://firebase.google.com/docs)
