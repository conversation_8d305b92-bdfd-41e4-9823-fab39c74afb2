{"name": "food-delivery-backend", "version": "1.0.0", "description": "Backend API for Food Delivery App", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "knex migrate:latest", "seed": "knex seed:run", "docker:build": "docker build -t food-delivery-api .", "docker:run": "docker run -p 3000:3000 food-delivery-api"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "stripe": "^14.7.0", "razorpay": "^2.9.2", "socket.io": "^4.7.4", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.0", "node-cron": "^3.0.3", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "dotenv": "^16.3.1", "config": "^3.3.9", "winston": "^3.11.0", "express-winston": "^4.2.0", "pg": "^8.11.3", "knex": "^3.0.1", "mongoose": "^8.0.3", "firebase-admin": "^11.11.1", "google-auth-library": "^9.4.1", "googleapis": "^128.0.0", "aws-sdk": "^2.1498.0", "cloudinary": "^1.41.0", "geolib": "^3.3.4", "node-geocoder": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "@types/node": "^20.9.4", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0"}, "keywords": ["food-delivery", "api", "nodejs", "express", "microservices"], "author": "Food Delivery Team", "license": "MIT"}