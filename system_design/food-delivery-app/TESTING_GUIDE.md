# Food Delivery App - Testing Guide

This comprehensive testing guide covers all aspects of testing your food delivery mobile application, from unit tests to end-to-end testing and deployment verification.

## Testing Strategy Overview

### Testing Pyramid
1. **Unit Tests (70%)** - Fast, isolated tests for individual components
2. **Integration Tests (20%)** - Test component interactions and API integrations
3. **E2E Tests (10%)** - Full user journey testing across the entire app

## Prerequisites

### Testing Environment Setup

```bash
# Install testing dependencies
npm install --save-dev @testing-library/react-native @testing-library/jest-native jest-react-native detox

# Install additional testing utilities
npm install --save-dev react-test-renderer jest-environment-node supertest
```

### Test Configuration

Create `jest.config.js`:
```javascript
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  testPathIgnorePatterns: ['/node_modules/', '/android/', '/ios/'],
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|react-navigation|@react-navigation)/)'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.js',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## Unit Testing

### 1. Component Testing

#### Testing React Native Components
```javascript
// __tests__/components/RestaurantCard.test.js
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import RestaurantCard from '../../src/components/RestaurantCard';

const mockRestaurant = {
  id: '1',
  name: 'Test Restaurant',
  cuisine: 'Italian',
  rating: 4.5,
  deliveryTime: '30-45 min',
  deliveryFee: 2.99,
  image: 'https://example.com/image.jpg'
};

describe('RestaurantCard', () => {
  it('renders restaurant information correctly', () => {
    const { getByText } = render(
      <RestaurantCard restaurant={mockRestaurant} onPress={() => {}} />
    );

    expect(getByText('Test Restaurant')).toBeTruthy();
    expect(getByText('Italian')).toBeTruthy();
    expect(getByText('4.5')).toBeTruthy();
    expect(getByText('30-45 min')).toBeTruthy();
  });

  it('calls onPress when card is pressed', () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <RestaurantCard 
        restaurant={mockRestaurant} 
        onPress={mockOnPress}
        testID="restaurant-card"
      />
    );

    fireEvent.press(getByTestId('restaurant-card'));
    expect(mockOnPress).toHaveBeenCalledWith(mockRestaurant);
  });
});
```

#### Testing Redux Slices
```javascript
// __tests__/store/authSlice.test.js
import authReducer, { loginUser, logoutUser } from '../../src/store/slices/authSlice';

describe('authSlice', () => {
  const initialState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  };

  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle loginUser.pending', () => {
    const action = { type: loginUser.pending.type };
    const state = authReducer(initialState, action);
    expect(state.isLoading).toBe(true);
    expect(state.error).toBe(null);
  });

  it('should handle loginUser.fulfilled', () => {
    const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
    const action = {
      type: loginUser.fulfilled.type,
      payload: { user: mockUser, token: 'mock-token' }
    };
    const state = authReducer(initialState, action);
    expect(state.isLoading).toBe(false);
    expect(state.isAuthenticated).toBe(true);
    expect(state.user).toEqual(mockUser);
    expect(state.token).toBe('mock-token');
  });
});
```

### 2. Service Testing

#### Testing API Services
```javascript
// __tests__/services/authService.test.js
import axios from 'axios';
import { authService } from '../../src/services/authService';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockResponse = {
        data: {
          user: { id: '1', email: '<EMAIL>' },
          token: 'mock-token'
        }
      };
      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(result).toEqual(mockResponse.data);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('/auth/login'),
        { email: '<EMAIL>', password: 'password123' }
      );
    });

    it('should throw error on login failure', async () => {
      const mockError = {
        response: { data: { message: 'Invalid credentials' } }
      };
      mockedAxios.post.mockRejectedValue(mockError);

      await expect(authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow('Invalid credentials');
    });
  });
});
```

### 3. Utility Function Testing

```javascript
// __tests__/utils/validation.test.js
import { validateEmail, validatePhone, validatePassword } from '../../src/utils/validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should validate correct email formats', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email formats', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should validate correct phone formats', () => {
      expect(validatePhone('+1234567890')).toBe(true);
      expect(validatePhone('1234567890')).toBe(true);
    });

    it('should reject invalid phone formats', () => {
      expect(validatePhone('123')).toBe(false);
      expect(validatePhone('abc1234567')).toBe(false);
    });
  });
});
```

## Integration Testing

### 1. API Integration Tests

```javascript
// __tests__/integration/auth.integration.test.js
import request from 'supertest';
import app from '../../backend/src/server';

describe('Auth API Integration', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        userType: 'customer'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.token).toBeDefined();
    });

    it('should return error for duplicate email', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '+1234567890',
        password: 'password123',
        userType: 'customer'
      };

      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Second registration with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('email already exists');
    });
  });
});
```

### 2. Navigation Integration Tests

```javascript
// __tests__/integration/navigation.integration.test.js
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { store } from '../../src/store';
import AppNavigator from '../../src/navigation/AppNavigator';

const renderWithProviders = (component) => {
  return render(
    <Provider store={store}>
      <NavigationContainer>
        {component}
      </NavigationContainer>
    </Provider>
  );
};

describe('Navigation Integration', () => {
  it('should navigate from login to home after successful authentication', async () => {
    const { getByTestId, getByText } = renderWithProviders(<AppNavigator />);

    // Should start at login screen
    expect(getByText('Login')).toBeTruthy();

    // Fill login form and submit
    fireEvent.changeText(getByTestId('email-input'), '<EMAIL>');
    fireEvent.changeText(getByTestId('password-input'), 'password123');
    fireEvent.press(getByTestId('login-button'));

    // Should navigate to home screen after successful login
    await waitFor(() => {
      expect(getByText('Home')).toBeTruthy();
    });
  });
});
```

## End-to-End Testing with Detox

### 1. Detox Configuration

Create `.detoxrc.json`:
```json
{
  "testRunner": "jest",
  "runnerConfig": "e2e/config.json",
  "configurations": {
    "ios.sim.debug": {
      "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/FoodDeliveryApp.app",
      "build": "xcodebuild -workspace ios/FoodDeliveryApp.xcworkspace -scheme FoodDeliveryApp -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build",
      "type": "ios.simulator",
      "device": {
        "type": "iPhone 14"
      }
    },
    "android.emu.debug": {
      "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk",
      "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && cd ..",
      "type": "android.emulator",
      "device": {
        "avdName": "Pixel_4_API_30"
      }
    }
  }
}
```

### 2. E2E Test Examples

```javascript
// e2e/userJourney.e2e.js
describe('User Journey', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should complete full order journey', async () => {
    // Login
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.id('login-button')).tap();

    // Navigate to restaurant
    await element(by.id('restaurant-card-1')).tap();

    // Add item to cart
    await element(by.id('menu-item-1')).tap();
    await element(by.id('add-to-cart-button')).tap();

    // Go to cart
    await element(by.id('cart-button')).tap();

    // Proceed to checkout
    await element(by.id('checkout-button')).tap();

    // Complete payment
    await element(by.id('payment-method-card')).tap();
    await element(by.id('place-order-button')).tap();

    // Verify order confirmation
    await expect(element(by.text('Order Confirmed'))).toBeVisible();
  });

  it('should search for restaurants', async () => {
    await element(by.id('search-tab')).tap();
    await element(by.id('search-input')).typeText('Pizza');
    await element(by.id('search-button')).tap();

    await expect(element(by.id('restaurant-list'))).toBeVisible();
    await expect(element(by.text('Pizza'))).toBeVisible();
  });
});
```

## Performance Testing

### 1. React Native Performance

```javascript
// __tests__/performance/componentPerformance.test.js
import React from 'react';
import { render } from '@testing-library/react-native';
import { performance } from 'perf_hooks';
import RestaurantList from '../../src/components/RestaurantList';

describe('Component Performance', () => {
  it('should render large restaurant list within acceptable time', () => {
    const largeRestaurantList = Array.from({ length: 1000 }, (_, i) => ({
      id: i.toString(),
      name: `Restaurant ${i}`,
      cuisine: 'Italian',
      rating: 4.5,
    }));

    const startTime = performance.now();
    render(<RestaurantList restaurants={largeRestaurantList} />);
    const endTime = performance.now();

    const renderTime = endTime - startTime;
    expect(renderTime).toBeLessThan(1000); // Should render in less than 1 second
  });
});
```

### 2. Memory Usage Testing

```javascript
// __tests__/performance/memoryUsage.test.js
describe('Memory Usage', () => {
  it('should not have memory leaks in navigation', async () => {
    const initialMemory = process.memoryUsage().heapUsed;

    // Simulate navigation between screens multiple times
    for (let i = 0; i < 100; i++) {
      // Navigate to different screens
      // This would be implemented with your navigation logic
    }

    // Force garbage collection
    if (global.gc) {
      global.gc();
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;

    // Memory increase should be minimal
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB
  });
});
```

## Testing Commands

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test -- RestaurantCard.test.js

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e:ios
npm run test:e2e:android

# Run performance tests
npm run test:performance
```

## Manual Testing Checklist

### Functional Testing

#### Authentication Flow
- [ ] User registration with email/phone
- [ ] Email/SMS verification
- [ ] Login with valid credentials
- [ ] Login with invalid credentials
- [ ] Password reset functionality
- [ ] Social media login (if implemented)
- [ ] Logout functionality

#### Restaurant Discovery
- [ ] Browse restaurants by location
- [ ] Search restaurants by name/cuisine
- [ ] Filter restaurants (rating, delivery time, price)
- [ ] View restaurant details
- [ ] View restaurant menu
- [ ] Check restaurant availability

#### Order Management
- [ ] Add items to cart
- [ ] Modify cart items (quantity, customizations)
- [ ] Remove items from cart
- [ ] Apply promo codes/discounts
- [ ] Select delivery address
- [ ] Choose delivery time
- [ ] Complete payment process
- [ ] Track order status
- [ ] View order history

#### Payment Testing
- [ ] Credit/debit card payments
- [ ] Digital wallet payments
- [ ] Cash on delivery
- [ ] Payment failure handling
- [ ] Refund processing

### Device Testing

#### iOS Testing
- [ ] iPhone SE (small screen)
- [ ] iPhone 14 (standard screen)
- [ ] iPhone 14 Pro Max (large screen)
- [ ] iPad (tablet interface)
- [ ] Different iOS versions (14, 15, 16, 17)

#### Android Testing
- [ ] Small screen devices (5" and below)
- [ ] Medium screen devices (5"-6")
- [ ] Large screen devices (6" and above)
- [ ] Tablets
- [ ] Different Android versions (API 21-33)
- [ ] Different manufacturers (Samsung, Google, OnePlus)

### Performance Testing

#### App Performance
- [ ] App launch time (< 3 seconds)
- [ ] Screen transition smoothness
- [ ] Scroll performance in lists
- [ ] Image loading performance
- [ ] Memory usage monitoring
- [ ] Battery usage optimization

#### Network Testing
- [ ] Slow network conditions (2G, 3G)
- [ ] Network interruption handling
- [ ] Offline functionality
- [ ] API timeout handling
- [ ] Large data loading

### Accessibility Testing

- [ ] Screen reader compatibility
- [ ] Voice control functionality
- [ ] High contrast mode
- [ ] Large text support
- [ ] Color blind accessibility
- [ ] Keyboard navigation

## Continuous Integration Testing

### GitHub Actions Workflow

Create `.github/workflows/test.yml`:
```yaml
name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run unit tests
      run: npm test -- --coverage --watchAll=false
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
    
    - name: Build Android
      run: |
        cd android
        ./gradlew assembleDebug
```

## Test Data Management

### Mock Data Setup

```javascript
// src/testUtils/mockData.js
export const mockRestaurants = [
  {
    id: '1',
    name: 'Pizza Palace',
    cuisine: 'Italian',
    rating: 4.5,
    deliveryTime: '30-45 min',
    deliveryFee: 2.99,
    minimumOrder: 15.00,
  },
  // Add more mock data
];

export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  phone: '+1234567890',
  userType: 'customer',
};
```

## Reporting and Analytics

### Test Reporting

- Use Jest HTML Reporter for detailed test reports
- Integrate with CI/CD for automated test reporting
- Track test coverage trends over time
- Monitor test execution time and performance

### Quality Metrics

- **Code Coverage**: Maintain >80% coverage
- **Test Execution Time**: Keep under 5 minutes for full suite
- **Flaky Test Rate**: Keep under 5%
- **Bug Detection Rate**: Track bugs caught by tests vs. production

This comprehensive testing strategy ensures your food delivery app is robust, reliable, and ready for production deployment.
