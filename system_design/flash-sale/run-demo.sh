#!/bin/bash

# Flash Sale System Demo Runner
# This script demonstrates how to run the flash sale system

echo "=== Flash Sale System Demo ==="
echo ""

# Setup environment (for macOS with Homebrew)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍺 Setting up macOS environment..."

    # Setup Homebrew environment
    if [ -f "/opt/homebrew/bin/brew" ]; then
        eval "$(/opt/homebrew/bin/brew shellenv)"
        echo "✅ Homebrew environment loaded"
    fi

    # Setup Java environment
    if [ -d "/opt/homebrew/opt/openjdk@17" ]; then
        export JAVA_HOME="/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home"
        export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
        echo "✅ Java 17 environment configured"
    fi
fi

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed. Please install Java 17 or higher."
    echo "   On macOS: brew install openjdk@17"
    echo "   On Linux: sudo apt install openjdk-17-jdk"
    echo "   Or visit: https://adoptium.net/ to download Java"
    exit 1
fi

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed. Please install Maven 3.6+."
    echo "   On macOS: brew install maven"
    echo "   On Linux: sudo apt install maven"
    echo "   Or visit: https://maven.apache.org/install.html for installation instructions"
    exit 1
fi

echo "✅ Java and Maven are available"
echo ""

# Build the project
echo "🔨 Building the project..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi

echo "✅ Build successful"
echo ""

# Run tests
echo "🧪 Running tests..."
mvn test -q

if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Please check the error messages above."
    exit 1
fi

echo "✅ All tests passed"
echo ""

# Start Redis (if available)
if command -v redis-server &> /dev/null; then
    echo "🚀 Starting Redis server..."
    redis-server --daemonize yes --port 6379
    echo "✅ Redis server started on port 6379"
else
    echo "⚠️  Redis not found. The application will use embedded Redis for demo."
fi

echo ""

# Start the application
echo "🚀 Starting Flash Sale Application..."
echo "   The application will start on http://localhost:8080"
echo "   Press Ctrl+C to stop the application"
echo ""

# Run the demo
mvn spring-boot:run -Dspring-boot.run.arguments=demo

echo ""
echo "=== Demo completed ==="
echo ""
echo "📚 Next steps:"
echo "   1. Check the logs above to see the demo results"
echo "   2. Visit http://localhost:8080/api/v1/flash-sales/health to test the API"
echo "   3. Use the API endpoints documented in README.md"
echo "   4. Run 'mvn test' to execute all tests"
echo ""
echo "🔗 API Documentation:"
echo "   - Reserve item: POST /api/v1/flash-sales/{saleId}/reserve"
echo "   - Process payment: POST /api/v1/flash-sales/reservations/{reservationId}/payment"
echo "   - Get status: GET /api/v1/flash-sales/{saleId}/status"
echo "   - Health check: GET /api/v1/flash-sales/health"
