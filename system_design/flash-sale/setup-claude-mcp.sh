#!/bin/bash

# Claude Desktop MCP Setup Script
# This script sets up MCP servers for Claude Desktop

echo "🚀 Setting up MCP for Claude Desktop..."

# Create Claude config directory if it doesn't exist
CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
mkdir -p "$CLAUDE_CONFIG_DIR"

# Backup existing config if it exists
if [ -f "$CLAUDE_CONFIG_DIR/claude_desktop_config.json" ]; then
    echo "📋 Backing up existing Claude config..."
    cp "$CLAUDE_CONFIG_DIR/claude_desktop_config.json" "$CLAUDE_CONFIG_DIR/claude_desktop_config.json.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Copy our config file
echo "📝 Installing MCP configuration..."
cp claude_desktop_config.json "$CLAUDE_CONFIG_DIR/claude_desktop_config.json"

echo "✅ MCP configuration installed!"
echo ""
echo "📋 Configured MCP Servers:"
echo "  • filesystem - Access to /Users/<USER>/Documents"
echo "  • git - Git repository access"
echo "  • postgres - PostgreSQL database access"
echo "  • google-maps - Google Maps API (requires API key)"
echo "  • brave-search - Brave Search API (requires API key)"
echo "  • sequential-thinking - Advanced reasoning capabilities"
echo ""
echo "🔧 Next Steps:"
echo "1. Restart Claude Desktop if it's running"
echo "2. Add API keys for Google Maps and Brave Search if needed"
echo "3. Update repository paths in the config as needed"
echo ""
echo "📁 Config file location: $CLAUDE_CONFIG_DIR/claude_desktop_config.json"
echo ""
echo "🎉 Setup complete! Claude Desktop can now access MCP servers."
