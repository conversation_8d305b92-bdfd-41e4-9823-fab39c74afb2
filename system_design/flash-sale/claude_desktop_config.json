{"mcpServers": {"filesystem": {"command": "/opt/homebrew/bin/npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents"]}, "git": {"command": "/Users/<USER>/Library/Python/3.9/bin/uvx", "args": ["mcp-server-git", "--repository", "/Users/<USER>/coding"]}, "postgres": {"command": "/opt/homebrew/bin/npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localhost/mydb"]}, "google-maps": {"command": "/opt/homebrew/bin/npx", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "your-api-key-here"}}, "brave-search": {"command": "/opt/homebrew/bin/npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": "your-api-key-here"}}, "sequential-thinking": {"command": "/opt/homebrew/bin/npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}