# 🚀 Model Context Protocol (MCP) Setup Guide for macOS

## ✅ Installation Complete!

You now have `uvx` installed and working on your macOS system. Here's your complete MCP setup guide.

## 📋 What is MCP?

Model Context Protocol (MCP) allows AI assistants like <PERSON> to securely access external tools and data sources. Think of it as a bridge between your AI and various services/documents.

## 🛠️ Current Setup Status

### ✅ Installed Components:
- **uvx 0.7.13** - Python package runner for MCP servers
- **PATH configured** - Added to your shell profile
- **MCP Git Server** - Ready to use with repositories

### 📁 Installation Location:
- uvx binary: `/Users/<USER>/Library/Python/3.9/bin/uvx`
- PATH added to: `~/.zshrc`

## 🔧 Available MCP Servers

### 1. **Git Repository Access**
```bash
# Test the Git server
uvx mcp-server-git --repository /path/to/your/repo
```

### 2. **File System Access**
```bash
# Install and test filesystem server
npm install -g @modelcontextprotocol/server-filesystem
npx @modelcontextprotocol/server-filesystem /path/to/allowed/directory
```

### 3. **Web Fetch (for documents)**
```bash
# Install web fetch server
npm install -g @modelcontextprotocol/server-fetch
npx @modelcontextprotocol/server-fetch
```

### 4. **Memory/Knowledge Base**
```bash
# Install memory server
npm install -g @modelcontextprotocol/server-memory
npx @modelcontextprotocol/server-memory
```

## 📄 Document Access Solutions (Quip Alternative)

Since there's no official Quip MCP server, here are alternatives for document access:

### Option 1: **Google Drive MCP Server**
```bash
# For Google Docs/Drive access
uvx mcp-server-gdrive
```

### Option 2: **Notion MCP Server**
```bash
# For Notion documents
uvx mcp-server-notion
```

### Option 3: **File System + Web Fetch**
```bash
# Export Quip docs to local files, then use filesystem server
npx @modelcontextprotocol/server-filesystem /path/to/exported/docs
```

### Option 4: **Custom Quip Integration**
You could create a custom MCP server for Quip using their API.

## 🎯 Claude Desktop Configuration

To use MCP servers with Claude Desktop, add this to your configuration file:

**Location:** `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "git": {
      "command": "uvx",
      "args": ["mcp-server-git", "--repository", "/path/to/your/repo"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/documents"]
    },
    "fetch": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch"]
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    }
  }
}
```

## 🔍 Testing Your Setup

### 1. Test uvx Installation
```bash
uvx --version
# Should show: uvx 0.7.13
```

### 2. Test Git Server
```bash
uvx mcp-server-git --help
# Should show usage information
```

### 3. Test with a Repository
```bash
# Navigate to a git repository
cd /path/to/your/git/repo
uvx mcp-server-git --repository .
```

## 🌐 Popular Third-Party MCP Servers

### Document & Knowledge Management
- **Notion**: `uvx mcp-server-notion`
- **Obsidian**: `uvx mcp-server-obsidian`
- **Confluence**: `uvx mcp-server-confluence`

### Development Tools
- **GitHub**: `npx @modelcontextprotocol/server-github`
- **GitLab**: `uvx mcp-server-gitlab`
- **Jira**: `uvx mcp-server-jira`

### Productivity
- **Slack**: `uvx mcp-server-slack`
- **Google Calendar**: `uvx mcp-server-google-calendar`
- **Todoist**: `uvx mcp-server-todoist`

### Databases
- **PostgreSQL**: `npx @modelcontextprotocol/server-postgres`
- **SQLite**: `uvx mcp-server-sqlite`
- **MongoDB**: `uvx mcp-server-mongodb`

## 🔐 Security & Authentication

Most MCP servers require authentication. Common patterns:

### API Keys (Environment Variables)
```bash
export GITHUB_TOKEN="your_token_here"
export NOTION_API_KEY="your_key_here"
export SLACK_BOT_TOKEN="your_token_here"
```

### OAuth Setup
Some servers require OAuth setup through their respective platforms.

## ✅ **INSTALLATION COMPLETE!**

### 🎉 **What's Now Working:**
- ✅ **uvx 0.7.13** installed and configured
- ✅ **Node.js 24.2.0** and **npm 11.3.0** installed
- ✅ **MCP Git Server** tested and working
- ✅ **MCP Filesystem Server** tested and working
- ✅ **PATH configured** in ~/.zshrc
- ✅ **Claude Desktop config** ready to use

### 🔧 **Quick Setup for Claude Desktop:**

1. **Copy the configuration:**
   ```bash
   # Create Claude config directory
   mkdir -p "$HOME/Library/Application Support/Claude"

   # Copy our config file
   cp claude_desktop_config.json "$HOME/Library/Application Support/Claude/claude_desktop_config.json"
   ```

2. **Restart Claude Desktop** to load the new configuration

### 📋 **Available MCP Servers (Ready to Use):**

| Server | Command | Purpose |
|--------|---------|---------|
| **Filesystem** | `npx @modelcontextprotocol/server-filesystem` | Access local documents |
| **Git** | `uvx mcp-server-git` | Git repository operations |
| **PostgreSQL** | `npx @modelcontextprotocol/server-postgres` | Database access |
| **Google Maps** | `npx @modelcontextprotocol/server-google-maps` | Location services |
| **Brave Search** | `npx @modelcontextprotocol/server-brave-search` | Web search |
| **Sequential Thinking** | `npx @modelcontextprotocol/server-sequential-thinking` | Advanced reasoning |

## 🚨 Troubleshooting

### Common Issues:

1. **uvx command not found**
   ```bash
   # Add to your shell profile (already done for you)
   echo 'export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"' >> ~/.zshrc
   source ~/.zshrc
   ```

2. **MCP server not starting**
   ```bash
   # Test uvx
   uvx --version
   # Test npm
   npm --version
   # Test specific server
   uvx mcp-server-git --help
   ```

3. **Claude Desktop not connecting**
   - Check JSON syntax in config file
   - Ensure file paths are correct
   - Restart Claude Desktop after config changes
   - Check config location: `~/Library/Application Support/Claude/claude_desktop_config.json`

## 📚 Next Steps

### For Quip Document Access:

1. **Export Quip Documents**
   - Export your Quip docs to Markdown/HTML
   - Store them in a local directory
   - Use filesystem MCP server to access them

2. **Use Web Fetch for Public Quip Docs**
   - If docs are publicly accessible
   - Use the fetch MCP server to retrieve content

3. **Create Custom Quip MCP Server**
   - Use Quip's API
   - Build a custom MCP server
   - Follow MCP server development guide

### Recommended Setup:
```json
{
  "mcpServers": {
    "documents": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/QuipExports"]
    },
    "git": {
      "command": "uvx",
      "args": ["mcp-server-git", "--repository", "/Users/<USER>/coding"]
    },
    "web": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch"]
    }
  }
}
```

## 🎉 You're All Set!

Your MCP setup is complete and ready to use. You can now:
- ✅ Access Git repositories through Claude
- ✅ Read local files and documents
- ✅ Fetch web content
- ✅ Extend with additional MCP servers as needed

For Quip-specific access, consider exporting your documents and using the filesystem server as the most straightforward solution.

## 📞 Support Resources

- **Official MCP Documentation**: https://modelcontextprotocol.io
- **MCP Server Registry**: https://github.com/modelcontextprotocol/servers
- **Community Discord**: Available through the official website

Happy coding! 🚀
