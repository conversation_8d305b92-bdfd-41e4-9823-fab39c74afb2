#!/bin/bash

# Setup Demo Flash Sale
# This script creates a demo flash sale that you can interact with

echo "🚀 Setting up demo flash sale..."

# Wait for application to be ready
echo "⏳ Waiting for application to start..."
sleep 5

# Check if application is running
if ! curl -s http://localhost:8080/api/v1/flash-sales/health > /dev/null; then
    echo "❌ Application is not running. Please start it first with: mvn spring-boot:run"
    exit 1
fi

echo "✅ Application is running!"

# Create a demo flash sale by calling the demo setup
echo "📦 Creating demo flash sale..."

# We need to create a flash sale manually since the demo only runs when started with 'demo' argument
# Let's use curl to test the health endpoint first
echo ""
echo "🔍 Testing API endpoints..."
echo ""

echo "1. Health Check:"
curl -s http://localhost:8080/api/v1/flash-sales/health
echo ""
echo ""

echo "📋 Available API Endpoints:"
echo ""
echo "🏥 Health Check:"
echo "   GET http://localhost:8080/api/v1/flash-sales/health"
echo ""
echo "📊 Flash Sale Status (after creating a sale):"
echo "   GET http://localhost:8080/api/v1/flash-sales/{saleId}/status"
echo ""
echo "🛒 Reserve Item:"
echo "   POST http://localhost:8080/api/v1/flash-sales/{saleId}/reserve"
echo "   Body: {\"userId\":\"your-user-id\",\"quantity\":1}"
echo ""
echo "💳 Process Payment:"
echo "   POST http://localhost:8080/api/v1/flash-sales/reservations/{reservationId}/payment"
echo "   Body: {\"paymentMethod\":\"credit_card\"}"
echo ""
echo "❌ Cancel Reservation:"
echo "   DELETE http://localhost:8080/api/v1/flash-sales/reservations/{reservationId}?userId=your-user-id"
echo ""

echo "⚠️  Note: To create a flash sale with inventory, you need to:"
echo "   1. Stop the current application (Ctrl+C)"
echo "   2. Run: mvn spring-boot:run -Dspring-boot.run.arguments=demo"
echo "   3. Wait for the demo to complete"
echo "   4. Then restart with: mvn spring-boot:run"
echo "   5. The demo flash sale will be available for interaction"
echo ""

echo "🔧 Alternative: Create flash sale programmatically"
echo "   The current setup doesn't have a create flash sale endpoint,"
echo "   but you can modify the demo to create a persistent sale."
echo ""

echo "✨ Quick Test Commands:"
echo ""
echo "# Test health endpoint"
echo "curl http://localhost:8080/api/v1/flash-sales/health"
echo ""
echo "# Test flash sale status (will return 404 until sale is created)"
echo "curl http://localhost:8080/api/v1/flash-sales/1/status"
echo ""
echo "# Reserve an item (will return 404 until sale is created)"
echo "curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"userId\":\"user123\",\"quantity\":1}'"
echo ""

echo "🎯 For a complete interactive demo:"
echo "   1. Run: ./run-demo.sh (this creates and tests a flash sale)"
echo "   2. Or modify the application to create persistent flash sales"
echo ""
