# 🎮 Interactive Flash Sale System Guide

## 🚀 Your Flash Sale System is LIVE!

**Base URL**: `http://localhost:8080/api/v1`

**Demo Flash Sale Created**: 
- **ID**: 1
- **Product**: Demo Phone Pro Max ($999.99)
- **Inventory**: 100 items available
- **Status**: ACTIVE (ends in ~1 hour)
- **Max per user**: 1 item

---

## 🔥 Quick Start Commands

### 1. Check Flash Sale Status
```bash
curl http://localhost:8080/api/v1/flash-sales/1/status
```

### 2. Reserve an Item (Replace "yourname" with your actual name)
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"yourname","quantity":1}'
```

### 3. Process Payment (Replace {reservationId} with the ID from step 2)
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/reservations/{reservationId}/payment \
  -H "Content-Type: application/json" \
  -d '{"paymentMethod":"credit_card"}'
```

### 4. Cancel Reservation (if needed)
```bash
curl -X DELETE "http://localhost:8080/api/v1/flash-sales/reservations/{reservationId}?userId=yourname"
```

---

## 📋 Complete API Reference

### 🏥 Health Check
```bash
curl http://localhost:8080/api/v1/flash-sales/health
```
**Expected Response**: `Flash Sale Service is running`

### 📊 Get Flash Sale Status
```bash
curl http://localhost:8080/api/v1/flash-sales/1/status
```
**Response Example**:
```json
{
  "id": 1,
  "productId": "DEMO_PHONE_001",
  "productName": "Demo Phone Pro Max",
  "price": 999.99,
  "totalInventory": 100,
  "availableInventory": 100,
  "startTime": "2025-06-17T16:06:33.331697",
  "endTime": "2025-06-17T17:11:33.331706",
  "status": "ACTIVE",
  "paymentTimeoutMinutes": 5,
  "maxPerUser": 1
}
```

### 🛒 Reserve Item
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"user123","quantity":1}'
```
**Success Response**:
```json
{
  "reservationId": 1,
  "userId": "user123",
  "quantity": 1,
  "expiresAt": "2025-06-17T16:16:33.123456",
  "message": "Reservation successful"
}
```

### 💳 Process Payment
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/reservations/1/payment \
  -H "Content-Type: application/json" \
  -d '{"paymentMethod":"credit_card"}'
```
**Success Response**:
```json
{
  "success": true,
  "orderId": 1,
  "paymentId": "PAY_ABC123",
  "amount": 999.99,
  "message": "Payment completed successfully"
}
```

### ❌ Cancel Reservation
```bash
curl -X DELETE "http://localhost:8080/api/v1/flash-sales/reservations/1?userId=user123"
```
**Success Response**: HTTP 204 No Content

### 🆕 Create New Flash Sale
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "CUSTOM_PRODUCT_001",
    "productName": "Custom Product",
    "totalInventory": 50,
    "price": 199.99,
    "startTime": "2025-06-17T17:00:00",
    "endTime": "2025-06-17T18:00:00",
    "paymentTimeoutMinutes": 5,
    "maxPerUser": 1
  }'
```

---

## 🎯 Step-by-Step Demo

### Step 1: Check Current Status
```bash
curl http://localhost:8080/api/v1/flash-sales/1/status
```
Note the `availableInventory` value.

### Step 2: Make Your First Reservation
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"demo-user-1","quantity":1}'
```
Save the `reservationId` from the response.

### Step 3: Check Status Again
```bash
curl http://localhost:8080/api/v1/flash-sales/1/status
```
Notice that `availableInventory` decreased by 1!

### Step 4: Complete the Purchase
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/reservations/{reservationId}/payment \
  -H "Content-Type: application/json" \
  -d '{"paymentMethod":"credit_card"}'
```
Replace `{reservationId}` with the actual ID from Step 2.

### Step 5: Try Multiple Users
Create reservations with different user IDs:
```bash
# User 2
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"demo-user-2","quantity":1}'

# User 3
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"demo-user-3","quantity":1}'
```

### Step 6: Test Error Scenarios

**Try to reserve again with same user**:
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"demo-user-1","quantity":1}'
```
Expected: Error message about existing reservation.

**Try to reserve more than allowed**:
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
  -H "Content-Type: application/json" \
  -d '{"userId":"demo-user-4","quantity":2}'
```
Expected: Error about exceeding max per user.

---

## 🔍 Monitoring & Testing

### Watch Inventory Changes
Run this command multiple times to see inventory decrease:
```bash
curl -s http://localhost:8080/api/v1/flash-sales/1/status | grep -o '"availableInventory":[0-9]*'
```

### Test Concurrent Access
Open multiple terminal windows and run reservations simultaneously to test the system's concurrency handling.

### Payment Simulation
The system simulates payment processing with:
- **80% success rate** (configurable)
- **1-2 second processing time**
- **Random failure scenarios** (card declined, network timeout, etc.)

---

## 🛠️ Advanced Testing

### Create Custom Flash Sale
```bash
curl -X POST http://localhost:8080/api/v1/flash-sales \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "LIMITED_EDITION_001",
    "productName": "Limited Edition Gadget",
    "totalInventory": 5,
    "price": 299.99,
    "startTime": "2025-06-17T16:30:00",
    "endTime": "2025-06-17T17:30:00",
    "paymentTimeoutMinutes": 3,
    "maxPerUser": 1
  }'
```

### Test High Concurrency
Use a script to simulate multiple users:
```bash
for i in {1..20}; do
  curl -X POST http://localhost:8080/api/v1/flash-sales/1/reserve \
    -H "Content-Type: application/json" \
    -d "{\"userId\":\"user$i\",\"quantity\":1}" &
done
wait
```

---

## 🎉 What You Can Observe

1. **Atomic Inventory Management**: No overselling even with concurrent requests
2. **Time-bound Reservations**: Reservations expire after 5 minutes
3. **Payment Simulation**: Realistic payment processing with failures
4. **Error Handling**: Proper HTTP status codes and error messages
5. **Real-time Updates**: Inventory changes immediately
6. **Concurrency Control**: Multiple users handled safely

---

## 🚨 Troubleshooting

**Application not responding?**
- Check if it's running: `curl http://localhost:8080/api/v1/flash-sales/health`
- Restart if needed: Stop with Ctrl+C and run `mvn spring-boot:run`

**Redis connection issues?**
- Check Redis status: `brew services list | grep redis`
- Start Redis: `brew services start redis`

**Database issues?**
- The app uses in-memory H2 database, restart the app to reset

---

## 🎯 Next Steps

1. **Try the interactive demo** with the commands above
2. **Test concurrent scenarios** with multiple users
3. **Observe the payment simulation** with success/failure rates
4. **Create your own flash sales** with custom parameters
5. **Monitor the logs** to see the system in action

**Enjoy exploring your flash sale system!** 🚀
