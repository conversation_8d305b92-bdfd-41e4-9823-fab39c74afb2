spring:
  application:
    name: flash-sale-system-test
  
  # Test Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: false
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  
  # Test Redis Configuration (embedded)
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 1000ms

# Test Server Configuration
server:
  port: 0 # Random port for tests

# Test Flash Sale Configuration
flashsale:
  payment:
    timeout-minutes: 1 # Shorter timeout for tests
    simulation:
      enabled: true
      success-rate: 1.0 # Always succeed in tests
      processing-time-ms: 100 # Faster processing
  
  inventory:
    cleanup-interval-seconds: 5 # More frequent cleanup
    reservation-ttl-minutes: 1
  
  monitoring:
    metrics-enabled: false
    log-level: WARN

# Test Logging Configuration
logging:
  level:
    com.flashsale: DEBUG
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
