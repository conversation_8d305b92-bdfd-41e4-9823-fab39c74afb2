package com.flashsale.service;

import com.flashsale.model.*;
import com.flashsale.repository.FlashSaleRepository;
import com.flashsale.repository.OrderRepository;
import com.flashsale.repository.ReservationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for FlashSaleService.
 * 
 * Tests the core business logic of flash sale operations including
 * reservation creation, payment processing, and error handling.
 */
@ExtendWith(MockitoExtension.class)
class FlashSaleServiceTest {
    
    @Mock
    private FlashSaleRepository flashSaleRepository;
    
    @Mock
    private ReservationRepository reservationRepository;
    
    @Mock
    private OrderRepository orderRepository;
    
    @Mock
    private InventoryService inventoryService;
    
    @Mock
    private PaymentService paymentService;
    
    @InjectMocks
    private FlashSaleService flashSaleService;
    
    private FlashSale testFlashSale;
    private Reservation testReservation;
    
    @BeforeEach
    void setUp() {
        // Create test flash sale
        testFlashSale = new FlashSale(
            "XPHONE_001",
            "XPhone Pro Max",
            1000,
            new BigDecimal("999.99"),
            LocalDateTime.now().minusMinutes(10),
            LocalDateTime.now().plusHours(1)
        );
        testFlashSale.setId(1L);
        testFlashSale.setStatus(com.flashsale.model.FlashSaleStatus.ACTIVE);
        
        // Create test reservation
        testReservation = new Reservation(
            testFlashSale,
            "user123",
            1,
            LocalDateTime.now().plusMinutes(5)
        );
        testReservation.setId(1L);
    }
    
    @Test
    void testReserveItem_Success() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        when(reservationRepository.hasExistingReservation(1L, "user123")).thenReturn(false);
        when(inventoryService.reserveInventory(1L, "user123", 1)).thenReturn(true);
        when(reservationRepository.save(any(Reservation.class))).thenReturn(testReservation);
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 1);
        
        // Assert
        assertTrue(result.isSuccess());
        assertEquals("Reservation successful", result.getMessage());
        assertNotNull(result.getReservation());
        
        verify(inventoryService).reserveInventory(1L, "user123", 1);
        verify(reservationRepository).save(any(Reservation.class));
    }
    
    @Test
    void testReserveItem_InsufficientInventory() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        when(reservationRepository.hasExistingReservation(1L, "user123")).thenReturn(false);
        when(inventoryService.reserveInventory(1L, "user123", 1)).thenReturn(false);
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 1);
        
        // Assert
        assertFalse(result.isSuccess());
        assertEquals("Insufficient inventory available", result.getMessage());
        assertNull(result.getReservation());
        
        verify(inventoryService).reserveInventory(1L, "user123", 1);
        verify(reservationRepository, never()).save(any(Reservation.class));
    }
    
    @Test
    void testReserveItem_UserAlreadyHasReservation() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        when(reservationRepository.hasExistingReservation(1L, "user123")).thenReturn(true);
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 1);
        
        // Assert
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("already has a reservation"));
        assertNull(result.getReservation());
        
        verify(inventoryService, never()).reserveInventory(anyLong(), anyString(), anyInt());
        verify(reservationRepository, never()).save(any(Reservation.class));
    }
    
    @Test
    void testReserveItem_FlashSaleNotFound() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.empty());
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 1);
        
        // Assert
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("Flash sale not found"));
        assertNull(result.getReservation());
    }
    
    @Test
    void testReserveItem_FlashSaleNotActive() {
        // Arrange
        testFlashSale.setStatus(com.flashsale.model.FlashSaleStatus.ENDED);
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 1);
        
        // Assert
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("not active"));
        assertNull(result.getReservation());
    }
    
    @Test
    void testReserveItem_ExceedsMaxPerUser() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        when(reservationRepository.hasExistingReservation(1L, "user123")).thenReturn(false);
        
        // Act
        FlashSaleService.ReservationResult result = flashSaleService.reserveItem(1L, "user123", 2);
        
        // Assert
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("exceeds maximum allowed"));
        assertNull(result.getReservation());
    }
    
    @Test
    void testCancelReservation_Success() {
        // Arrange
        when(reservationRepository.findById(1L)).thenReturn(Optional.of(testReservation));
        when(reservationRepository.save(any(Reservation.class))).thenReturn(testReservation);
        
        // Act
        boolean result = flashSaleService.cancelReservation(1L, "user123");
        
        // Assert
        assertTrue(result);
        verify(inventoryService).releaseInventory(
            testReservation.getFlashSale().getId(),
            testReservation.getUserId(),
            testReservation.getQuantity()
        );
        verify(reservationRepository).save(testReservation);
    }
    
    @Test
    void testCancelReservation_WrongUser() {
        // Arrange
        when(reservationRepository.findById(1L)).thenReturn(Optional.of(testReservation));
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            flashSaleService.cancelReservation(1L, "wronguser");
        });
        
        verify(inventoryService, never()).releaseInventory(anyLong(), anyString(), anyInt());
        verify(reservationRepository, never()).save(any(Reservation.class));
    }
    
    @Test
    void testCancelReservation_ReservationNotFound() {
        // Arrange
        when(reservationRepository.findById(1L)).thenReturn(Optional.empty());
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            flashSaleService.cancelReservation(1L, "user123");
        });
    }
    
    @Test
    void testGetFlashSaleStatus() {
        // Arrange
        when(flashSaleRepository.findById(1L)).thenReturn(Optional.of(testFlashSale));
        when(inventoryService.getAvailableInventory(1L)).thenReturn(500);
        
        // Act
        FlashSaleService.FlashSaleStatus status = flashSaleService.getFlashSaleStatus(1L);
        
        // Assert
        assertNotNull(status);
        assertEquals(testFlashSale, status.getFlashSale());
        assertEquals(Integer.valueOf(500), status.getAvailableInventory());
    }
}
