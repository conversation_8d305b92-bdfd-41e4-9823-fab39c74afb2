package com.flashsale.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flashsale.FlashSaleApplication;
import com.flashsale.dto.ReservationRequest;
import com.flashsale.dto.PaymentRequest;
import com.flashsale.model.FlashSale;
import com.flashsale.model.FlashSaleStatus;
import com.flashsale.repository.FlashSaleRepository;
import com.flashsale.service.InventoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the Flash Sale system.
 * 
 * Tests the complete flow from API endpoints through to database
 * and Redis operations.
 */
@SpringBootTest(classes = FlashSaleApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class FlashSaleIntegrationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private FlashSaleRepository flashSaleRepository;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    private FlashSale testFlashSale;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // Create and save test flash sale
        testFlashSale = new FlashSale(
            "XPHONE_001",
            "XPhone Pro Max",
            100,
            new BigDecimal("999.99"),
            LocalDateTime.now().minusMinutes(10),
            LocalDateTime.now().plusHours(1)
        );
        testFlashSale.setStatus(FlashSaleStatus.ACTIVE);
        testFlashSale = flashSaleRepository.save(testFlashSale);
        
        // Initialize inventory in Redis
        inventoryService.initializeInventory(testFlashSale.getId(), testFlashSale.getTotalInventory());
    }
    
    @Test
    void testCompleteFlashSaleFlow() throws Exception {
        String userId = "user123";
        
        // 1. Check flash sale status
        mockMvc.perform(get("/api/v1/flash-sales/{saleId}/status", testFlashSale.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testFlashSale.getId()))
                .andExpect(jsonPath("$.productId").value("XPHONE_001"))
                .andExpect(jsonPath("$.availableInventory").value(100))
                .andExpect(jsonPath("$.status").value("ACTIVE"));
        
        // 2. Reserve an item
        ReservationRequest reservationRequest = new ReservationRequest(userId, 1);
        
        String reservationResponse = mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reservationRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.reservationId").exists())
                .andExpect(jsonPath("$.userId").value(userId))
                .andExpect(jsonPath("$.quantity").value(1))
                .andExpect(jsonPath("$.expiresAt").exists())
                .andReturn().getResponse().getContentAsString();
        
        // Extract reservation ID from response
        Long reservationId = objectMapper.readTree(reservationResponse).get("reservationId").asLong();
        
        // 3. Check inventory decreased
        mockMvc.perform(get("/api/v1/flash-sales/{saleId}/status", testFlashSale.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.availableInventory").value(99));
        
        // 4. Process payment
        PaymentRequest paymentRequest = new PaymentRequest("credit_card");
        
        mockMvc.perform(post("/api/v1/flash-sales/reservations/{reservationId}/payment", reservationId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(paymentRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.orderId").exists())
                .andExpect(jsonPath("$.amount").value(999.99));
    }
    
    @Test
    void testReservationWithInsufficientInventory() throws Exception {
        // Set inventory to 0
        inventoryService.initializeInventory(testFlashSale.getId(), 0);
        
        ReservationRequest request = new ReservationRequest("user123", 1);
        
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("Insufficient inventory available"));
    }
    
    @Test
    void testDuplicateReservation() throws Exception {
        String userId = "user123";
        ReservationRequest request = new ReservationRequest(userId, 1);
        
        // First reservation should succeed
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());
        
        // Second reservation should fail
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("User already has a reservation for this flash sale"));
    }
    
    @Test
    void testInvalidQuantity() throws Exception {
        ReservationRequest request = new ReservationRequest("user123", 0);
        
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testExceedsMaxPerUser() throws Exception {
        ReservationRequest request = new ReservationRequest("user123", 2); // Max is 1
        
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", testFlashSale.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("Quantity exceeds maximum allowed per user"));
    }
    
    @Test
    void testFlashSaleNotFound() throws Exception {
        ReservationRequest request = new ReservationRequest("user123", 1);
        
        mockMvc.perform(post("/api/v1/flash-sales/{saleId}/reserve", 99999L)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Invalid request: Flash sale not found: 99999"));
    }
    
    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/v1/flash-sales/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Flash Sale Service is running"));
    }
}
