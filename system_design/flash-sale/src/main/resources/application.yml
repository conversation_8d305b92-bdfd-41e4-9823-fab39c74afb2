spring:
  application:
    name: flash-sale-system
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:flashsale;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      jedis:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api/v1

# Flash Sale Configuration
flashsale:
  payment:
    timeout-minutes: 5
    simulation:
      enabled: true
      success-rate: 0.8
      processing-time-ms: 1000
  
  inventory:
    cleanup-interval-seconds: 30
    reservation-ttl-minutes: 5
  
  monitoring:
    metrics-enabled: true
    log-level: INFO

# Logging Configuration
logging:
  level:
    com.flashsale: INFO
    org.springframework.data.redis: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
