package com.flashsale.controller;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.flashsale.dto.CreateFlashSaleRequest;
import com.flashsale.dto.FlashSaleStatusResponse;
import com.flashsale.dto.PaymentRequest;
import com.flashsale.dto.PaymentResponse;
import com.flashsale.dto.ReservationRequest;
import com.flashsale.dto.ReservationResponse;
import com.flashsale.service.FlashSaleManagementService;
import com.flashsale.service.FlashSaleService;

import jakarta.validation.Valid;

/**
 * REST controller for flash sale operations.
 * 
 * Provides endpoints for:
 * - Reserving items in flash sales
 * - Processing payments
 * - Checking flash sale status
 * - Managing reservations
 */
@RestController
@RequestMapping("/flash-sales")
@CrossOrigin(origins = "*")
public class FlashSaleController {
    
    private static final Logger logger = LoggerFactory.getLogger(FlashSaleController.class);
    
    @Autowired
    private FlashSaleService flashSaleService;

    @Autowired
    private FlashSaleManagementService managementService;
    
    /**
     * Reserve an item in a flash sale.
     * 
     * POST /api/v1/flash-sales/{saleId}/reserve
     */
    @PostMapping("/{saleId}/reserve")
    public ResponseEntity<ReservationResponse> reserveItem(
            @PathVariable Long saleId,
            @Valid @RequestBody ReservationRequest request) {
        
        logger.info("Received reservation request for flash sale {} from user {}", 
                   saleId, request.getUserId());
        
        try {
            FlashSaleService.ReservationResult result = flashSaleService.reserveItem(
                saleId, request.getUserId(), request.getQuantity());
            
            if (result.isSuccess()) {
                ReservationResponse response = new ReservationResponse(
                    result.getReservation().getId(),
                    result.getReservation().getUserId(),
                    result.getReservation().getQuantity(),
                    result.getReservation().getExpiresAt(),
                    result.getMessage()
                );
                
                logger.info("Reservation successful for user {} in flash sale {}", 
                           request.getUserId(), saleId);
                
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
            } else {
                ReservationResponse response = new ReservationResponse(
                    null, request.getUserId(), request.getQuantity(), null, result.getMessage());
                
                logger.warn("Reservation failed for user {} in flash sale {}: {}", 
                           request.getUserId(), saleId, result.getMessage());
                
                return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
            }
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid reservation request for flash sale {}: {}", saleId, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new ReservationResponse(null, request.getUserId(), request.getQuantity(), 
                                            null, "Invalid request: " + e.getMessage()));
        } catch (IllegalStateException e) {
            logger.warn("Reservation not allowed for flash sale {}: {}", saleId, e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new ReservationResponse(null, request.getUserId(), request.getQuantity(), 
                                            null, e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during reservation for flash sale {}", saleId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ReservationResponse(null, request.getUserId(), request.getQuantity(), 
                                            null, "Internal server error"));
        }
    }
    
    /**
     * Process payment for a reservation.
     * 
     * POST /api/v1/flash-sales/reservations/{reservationId}/payment
     */
    @PostMapping("/reservations/{reservationId}/payment")
    public CompletableFuture<ResponseEntity<PaymentResponse>> processPayment(
            @PathVariable Long reservationId,
            @Valid @RequestBody PaymentRequest request) {
        
        logger.info("Received payment request for reservation {}", reservationId);
        
        return flashSaleService.processPayment(reservationId)
            .thenApply(result -> {
                if (result.isSuccess()) {
                    PaymentResponse response = new PaymentResponse(
                        true,
                        result.getOrder().getId(),
                        result.getOrder().getPaymentId(),
                        result.getOrder().getAmount(),
                        result.getMessage()
                    );
                    
                    logger.info("Payment successful for reservation {}", reservationId);
                    return ResponseEntity.ok(response);
                } else {
                    PaymentResponse response = new PaymentResponse(
                        false, null, null, null, result.getMessage());
                    
                    logger.warn("Payment failed for reservation {}: {}", reservationId, result.getMessage());
                    return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED).body(response);
                }
            })
            .exceptionally(throwable -> {
                logger.error("Unexpected error during payment processing for reservation {}", 
                           reservationId, throwable);
                PaymentResponse response = new PaymentResponse(
                    false, null, null, null, "Payment processing error");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            });
    }
    
    /**
     * Get flash sale status and current inventory.
     * 
     * GET /api/v1/flash-sales/{saleId}/status
     */
    @GetMapping("/{saleId}/status")
    public ResponseEntity<FlashSaleStatusResponse> getFlashSaleStatus(@PathVariable Long saleId) {
        
        logger.debug("Received status request for flash sale {}", saleId);
        
        try {
            FlashSaleService.FlashSaleStatus status = flashSaleService.getFlashSaleStatus(saleId);
            
            FlashSaleStatusResponse response = new FlashSaleStatusResponse(
                status.getFlashSale().getId(),
                status.getFlashSale().getProductId(),
                status.getFlashSale().getProductName(),
                status.getFlashSale().getPrice(),
                status.getFlashSale().getTotalInventory(),
                status.getAvailableInventory(),
                status.getFlashSale().getStartTime(),
                status.getFlashSale().getEndTime(),
                status.getFlashSale().getStatus().toString(),
                status.getFlashSale().getPaymentTimeoutMinutes(),
                status.getFlashSale().getMaxPerUser()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Flash sale not found: {}", saleId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error retrieving status for flash sale {}", saleId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Cancel a reservation.
     * 
     * DELETE /api/v1/flash-sales/reservations/{reservationId}
     */
    @DeleteMapping("/reservations/{reservationId}")
    public ResponseEntity<Void> cancelReservation(
            @PathVariable Long reservationId,
            @RequestParam String userId) {
        
        logger.info("Received cancellation request for reservation {} from user {}", 
                   reservationId, userId);
        
        try {
            boolean cancelled = flashSaleService.cancelReservation(reservationId, userId);
            
            if (cancelled) {
                logger.info("Reservation {} cancelled successfully", reservationId);
                return ResponseEntity.noContent().build();
            } else {
                logger.warn("Failed to cancel reservation {} for user {}", reservationId, userId);
                return ResponseEntity.status(HttpStatus.CONFLICT).build();
            }
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid cancellation request for reservation {}: {}", reservationId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error cancelling reservation {}", reservationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Create a new flash sale.
     *
     * POST /api/v1/flash-sales
     */
    @PostMapping
    public ResponseEntity<FlashSaleStatusResponse> createFlashSale(
            @Valid @RequestBody CreateFlashSaleRequest request) {

        logger.info("Received create flash sale request for product: {}", request.getProductId());

        try {
            com.flashsale.model.FlashSale flashSale = managementService.createFlashSale(request);

            FlashSaleStatusResponse response = new FlashSaleStatusResponse(
                flashSale.getId(),
                flashSale.getProductId(),
                flashSale.getProductName(),
                flashSale.getPrice(),
                flashSale.getTotalInventory(),
                flashSale.getAvailableInventory(),
                flashSale.getStartTime(),
                flashSale.getEndTime(),
                flashSale.getStatus().toString(),
                flashSale.getPaymentTimeoutMinutes(),
                flashSale.getMaxPerUser()
            );

            logger.info("Created flash sale: {}", flashSale.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid create flash sale request: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error creating flash sale", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Create a demo flash sale for testing.
     *
     * POST /api/v1/flash-sales/demo
     */
    @PostMapping("/demo")
    public ResponseEntity<FlashSaleStatusResponse> createDemoFlashSale() {

        logger.info("Creating demo flash sale");

        try {
            com.flashsale.model.FlashSale flashSale = managementService.createDemoFlashSale();

            FlashSaleStatusResponse response = new FlashSaleStatusResponse(
                flashSale.getId(),
                flashSale.getProductId(),
                flashSale.getProductName(),
                flashSale.getPrice(),
                flashSale.getTotalInventory(),
                flashSale.getAvailableInventory(),
                flashSale.getStartTime(),
                flashSale.getEndTime(),
                flashSale.getStatus().toString(),
                flashSale.getPaymentTimeoutMinutes(),
                flashSale.getMaxPerUser()
            );

            logger.info("Created demo flash sale: {}", flashSale.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (Exception e) {
            logger.error("Error creating demo flash sale", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint.
     *
     * GET /api/v1/flash-sales/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Flash Sale Service is running");
    }
}
