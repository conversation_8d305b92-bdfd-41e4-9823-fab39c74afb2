package com.flashsale.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing a flash sale event.
 * 
 * This entity stores the core information about a flash sale including
 * inventory limits, timing, and current status.
 */
@Entity
@Table(name = "flash_sales")
public class FlashSale {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "product_id", nullable = false)
    private String productId;
    
    @NotBlank
    @Column(name = "product_name", nullable = false)
    private String productName;
    
    @NotNull
    @Min(1)
    @Column(name = "total_inventory", nullable = false)
    private Integer totalInventory;
    
    @NotNull
    @Min(0)
    @Column(name = "available_inventory", nullable = false)
    private Integer availableInventory;
    
    @NotNull
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    @NotNull
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @NotNull
    @Column(name = "end_time", nullable = false)
    private LocalDateTime endTime;
    
    @Column(name = "payment_timeout_minutes")
    private Integer paymentTimeoutMinutes = 5;
    
    @Column(name = "max_per_user")
    private Integer maxPerUser = 1;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private FlashSaleStatus status = FlashSaleStatus.SCHEDULED;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public FlashSale() {}
    
    public FlashSale(String productId, String productName, Integer totalInventory, 
                     BigDecimal price, LocalDateTime startTime, LocalDateTime endTime) {
        this.productId = productId;
        this.productName = productName;
        this.totalInventory = totalInventory;
        this.availableInventory = totalInventory;
        this.price = price;
        this.startTime = startTime;
        this.endTime = endTime;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return status == FlashSaleStatus.ACTIVE && 
               now.isAfter(startTime) && 
               now.isBefore(endTime);
    }
    
    public boolean hasInventoryAvailable() {
        return availableInventory > 0;
    }
    
    public boolean canReserve(int quantity) {
        return hasInventoryAvailable() && availableInventory >= quantity;
    }
    
    public void reserveInventory(int quantity) {
        if (!canReserve(quantity)) {
            throw new IllegalStateException("Insufficient inventory available");
        }
        this.availableInventory -= quantity;
    }
    
    public void releaseInventory(int quantity) {
        this.availableInventory = Math.min(totalInventory, availableInventory + quantity);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getProductId() { return productId; }
    public void setProductId(String productId) { this.productId = productId; }
    
    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }
    
    public Integer getTotalInventory() { return totalInventory; }
    public void setTotalInventory(Integer totalInventory) { this.totalInventory = totalInventory; }
    
    public Integer getAvailableInventory() { return availableInventory; }
    public void setAvailableInventory(Integer availableInventory) { this.availableInventory = availableInventory; }
    
    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    
    public LocalDateTime getEndTime() { return endTime; }
    public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    
    public Integer getPaymentTimeoutMinutes() { return paymentTimeoutMinutes; }
    public void setPaymentTimeoutMinutes(Integer paymentTimeoutMinutes) { this.paymentTimeoutMinutes = paymentTimeoutMinutes; }
    
    public Integer getMaxPerUser() { return maxPerUser; }
    public void setMaxPerUser(Integer maxPerUser) { this.maxPerUser = maxPerUser; }
    
    public FlashSaleStatus getStatus() { return status; }
    public void setStatus(FlashSaleStatus status) { this.status = status; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    @Override
    public String toString() {
        return "FlashSale{" +
                "id=" + id +
                ", productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", totalInventory=" + totalInventory +
                ", availableInventory=" + availableInventory +
                ", price=" + price +
                ", status=" + status +
                '}';
    }
}
