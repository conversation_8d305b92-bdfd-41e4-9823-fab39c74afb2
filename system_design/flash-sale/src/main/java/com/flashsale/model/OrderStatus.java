package com.flashsale.model;

/**
 * Enumeration representing the various states of an order.
 */
public enum OrderStatus {
    /**
     * Order is created but payment not yet completed
     */
    PENDING,
    
    /**
     * Order payment completed successfully
     */
    COMPLETED,
    
    /**
     * Order payment failed
     */
    FAILED,
    
    /**
     * Order was cancelled
     */
    CANCELLED,
    
    /**
     * Order is being processed for fulfillment
     */
    PROCESSING,
    
    /**
     * Order has been shipped
     */
    SHIPPED,
    
    /**
     * Order has been delivered
     */
    DELIVERED
}
