package com.flashsale.model;

/**
 * Enumeration representing the various states of a flash sale.
 */
public enum FlashSaleStatus {
    /**
     * Sale is scheduled but not yet started
     */
    SCHEDULED,
    
    /**
     * Sale is currently active and accepting reservations
     */
    ACTIVE,
    
    /**
     * Sale has ended normally (time expired or inventory exhausted)
     */
    ENDED,
    
    /**
     * Sale was cancelled before completion
     */
    CANCELLED,
    
    /**
     * Sale is temporarily paused (can be resumed)
     */
    PAUSED
}
