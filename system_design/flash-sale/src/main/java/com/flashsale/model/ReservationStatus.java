package com.flashsale.model;

/**
 * Enumeration representing the various states of a reservation.
 */
public enum ReservationStatus {
    /**
     * Reservation is active and waiting for payment
     */
    RESERVED,
    
    /**
     * Payment is currently being processed
     */
    PAYMENT_PROCESSING,
    
    /**
     * Payment completed successfully, reservation fulfilled
     */
    COMPLETED,
    
    /**
     * Reservation expired due to timeout
     */
    EXPIRED,
    
    /**
     * Reservation was manually cancelled
     */
    CANCELLED,
    
    /**
     * Payment processing failed
     */
    PAYMENT_FAILED
}
