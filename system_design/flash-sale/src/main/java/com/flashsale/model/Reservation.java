package com.flashsale.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Entity representing a user's reservation for items in a flash sale.
 * 
 * Reservations are time-bound and automatically expire if payment
 * is not completed within the specified timeout period.
 */
@Entity
@Table(name = "reservations", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"flash_sale_id", "user_id"}))
public class Reservation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "flash_sale_id", nullable = false)
    private FlashSale flashSale;
    
    @NotBlank
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @NotNull
    @Min(1)
    @Column(name = "quantity", nullable = false)
    private Integer quantity;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ReservationStatus status = ReservationStatus.RESERVED;
    
    @Column(name = "reserved_at")
    private LocalDateTime reservedAt;
    
    @NotNull
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    @Column(name = "payment_id")
    private String paymentId;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    // Constructors
    public Reservation() {}
    
    public Reservation(FlashSale flashSale, String userId, Integer quantity, LocalDateTime expiresAt) {
        this.flashSale = flashSale;
        this.userId = userId;
        this.quantity = quantity;
        this.expiresAt = expiresAt;
        this.reservedAt = LocalDateTime.now();
    }
    
    // Business methods
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    public boolean isActive() {
        return status == ReservationStatus.RESERVED && !isExpired();
    }
    
    public boolean canProcessPayment() {
        return isActive() && paymentId == null;
    }
    
    public void markAsPaymentProcessing(String paymentId) {
        if (!canProcessPayment()) {
            throw new IllegalStateException("Cannot process payment for this reservation");
        }
        this.paymentId = paymentId;
        this.status = ReservationStatus.PAYMENT_PROCESSING;
    }
    
    public void markAsCompleted() {
        this.status = ReservationStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }
    
    public void markAsExpired() {
        this.status = ReservationStatus.EXPIRED;
    }
    
    public void markAsCancelled() {
        this.status = ReservationStatus.CANCELLED;
    }
    
    public void markAsPaymentFailed() {
        this.status = ReservationStatus.PAYMENT_FAILED;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public FlashSale getFlashSale() { return flashSale; }
    public void setFlashSale(FlashSale flashSale) { this.flashSale = flashSale; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public Integer getQuantity() { return quantity; }
    public void setQuantity(Integer quantity) { this.quantity = quantity; }
    
    public ReservationStatus getStatus() { return status; }
    public void setStatus(ReservationStatus status) { this.status = status; }
    
    public LocalDateTime getReservedAt() { return reservedAt; }
    public void setReservedAt(LocalDateTime reservedAt) { this.reservedAt = reservedAt; }
    
    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    
    public String getPaymentId() { return paymentId; }
    public void setPaymentId(String paymentId) { this.paymentId = paymentId; }
    
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    
    @Override
    public String toString() {
        return "Reservation{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", quantity=" + quantity +
                ", status=" + status +
                ", reservedAt=" + reservedAt +
                ", expiresAt=" + expiresAt +
                '}';
    }
}
