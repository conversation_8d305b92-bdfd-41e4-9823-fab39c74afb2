package com.flashsale.demo;

import com.flashsale.model.FlashSale;
import com.flashsale.model.FlashSaleStatus;
import com.flashsale.repository.FlashSaleRepository;
import com.flashsale.service.FlashSaleService;
import com.flashsale.service.InventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Demo class to showcase the flash sale system functionality.
 * 
 * This class demonstrates:
 * - Creating a flash sale
 * - Concurrent user reservations
 * - Payment processing
 * - Error handling scenarios
 */
@Component
public class FlashSaleDemo implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(FlashSaleDemo.class);
    
    @Autowired
    private FlashSaleRepository flashSaleRepository;
    
    @Autowired
    private FlashSaleService flashSaleService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Override
    public void run(String... args) throws Exception {
        if (args.length > 0 && "demo".equals(args[0])) {
            logger.info("Starting Flash Sale Demo...");
            runDemo();
        }
    }
    
    private void runDemo() throws Exception {
        // 1. Create a flash sale
        FlashSale flashSale = createDemoFlashSale();
        logger.info("Created flash sale: {}", flashSale);
        
        // 2. Initialize inventory in Redis
        inventoryService.initializeInventory(flashSale.getId(), flashSale.getTotalInventory());
        logger.info("Initialized inventory: {} items", flashSale.getTotalInventory());
        
        // 3. Simulate concurrent users trying to reserve items
        simulateConcurrentReservations(flashSale.getId(), 50, 10);
        
        // 4. Check final status
        checkFinalStatus(flashSale.getId());
        
        logger.info("Flash Sale Demo completed!");
    }
    
    private FlashSale createDemoFlashSale() {
        FlashSale flashSale = new FlashSale(
            "DEMO_PHONE_001",
            "Demo Phone Pro Max",
            10, // Small inventory for demo
            new BigDecimal("999.99"),
            LocalDateTime.now().minusMinutes(5), // Started 5 minutes ago
            LocalDateTime.now().plusMinutes(30)  // Ends in 30 minutes
        );
        flashSale.setStatus(FlashSaleStatus.ACTIVE);
        flashSale.setPaymentTimeoutMinutes(2); // Short timeout for demo
        
        return flashSaleRepository.save(flashSale);
    }
    
    private void simulateConcurrentReservations(Long flashSaleId, int totalUsers, int inventory) throws Exception {
        logger.info("Simulating {} concurrent users trying to reserve from {} items", totalUsers, inventory);
        
        ExecutorService executor = Executors.newFixedThreadPool(20);
        CompletableFuture<Void>[] futures = new CompletableFuture[totalUsers];
        
        // Create concurrent reservation attempts
        for (int i = 0; i < totalUsers; i++) {
            final String userId = "user" + (i + 1);
            final int userIndex = i;
            
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    // Simulate some delay between users
                    Thread.sleep(userIndex * 10);
                    
                    // Attempt reservation
                    FlashSaleService.ReservationResult result = 
                        flashSaleService.reserveItem(flashSaleId, userId, 1);
                    
                    if (result.isSuccess()) {
                        logger.info("✅ User {} successfully reserved item (Reservation ID: {})", 
                                   userId, result.getReservation().getId());
                        
                        // Simulate payment processing for successful reservations
                        simulatePayment(result.getReservation().getId(), userId);
                        
                    } else {
                        logger.warn("❌ User {} failed to reserve: {}", userId, result.getMessage());
                    }
                    
                } catch (Exception e) {
                    logger.error("Error for user {}: {}", userId, e.getMessage());
                }
            }, executor);
        }
        
        // Wait for all attempts to complete
        CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        logger.info("All reservation attempts completed");
    }
    
    private void simulatePayment(Long reservationId, String userId) {
        try {
            // Add some delay to simulate user payment time
            Thread.sleep(500 + (int)(Math.random() * 1000));
            
            CompletableFuture<FlashSaleService.PaymentResult> paymentFuture = 
                flashSaleService.processPayment(reservationId);
            
            paymentFuture.thenAccept(result -> {
                if (result.isSuccess()) {
                    logger.info("💳 User {} payment successful (Order ID: {})", 
                               userId, result.getOrder().getId());
                } else {
                    logger.warn("💳 User {} payment failed: {}", userId, result.getMessage());
                }
            }).exceptionally(throwable -> {
                logger.error("💳 User {} payment error: {}", userId, throwable.getMessage());
                return null;
            });
            
        } catch (Exception e) {
            logger.error("Error simulating payment for user {}: {}", userId, e.getMessage());
        }
    }
    
    private void checkFinalStatus(Long flashSaleId) {
        try {
            Thread.sleep(3000); // Wait for payments to process
            
            FlashSaleService.FlashSaleStatus status = flashSaleService.getFlashSaleStatus(flashSaleId);
            
            logger.info("=== FINAL STATUS ===");
            logger.info("Flash Sale ID: {}", status.getFlashSale().getId());
            logger.info("Product: {}", status.getFlashSale().getProductName());
            logger.info("Total Inventory: {}", status.getFlashSale().getTotalInventory());
            logger.info("Available Inventory: {}", status.getAvailableInventory());
            logger.info("Sold Items: {}", 
                       status.getFlashSale().getTotalInventory() - status.getAvailableInventory());
            logger.info("Status: {}", status.getFlashSale().getStatus());
            
        } catch (Exception e) {
            logger.error("Error checking final status", e);
        }
    }
}
