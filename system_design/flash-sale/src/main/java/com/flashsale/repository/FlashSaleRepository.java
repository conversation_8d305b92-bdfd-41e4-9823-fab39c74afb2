package com.flashsale.repository;

import com.flashsale.model.FlashSale;
import com.flashsale.model.FlashSaleStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import jakarta.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for FlashSale entity operations.
 * 
 * Provides data access methods with proper locking mechanisms
 * to handle concurrent access during high-traffic scenarios.
 */
@Repository
public interface FlashSaleRepository extends JpaRepository<FlashSale, Long> {
    
    /**
     * Find flash sale by product ID and status.
     */
    Optional<FlashSale> findByProductIdAndStatus(String productId, FlashSaleStatus status);
    
    /**
     * Find all active flash sales within the given time range.
     */
    @Query("SELECT fs FROM FlashSale fs WHERE fs.status = :status " +
           "AND fs.startTime <= :currentTime AND fs.endTime > :currentTime")
    List<FlashSale> findActiveFlashSales(@Param("status") FlashSaleStatus status, 
                                        @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find flash sale with pessimistic lock for inventory updates.
     * This prevents concurrent modifications during inventory operations.
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT fs FROM FlashSale fs WHERE fs.id = :id")
    Optional<FlashSale> findByIdWithLock(@Param("id") Long id);
    
    /**
     * Update available inventory atomically.
     * This method ensures inventory updates are atomic and consistent.
     */
    @Modifying
    @Query("UPDATE FlashSale fs SET fs.availableInventory = fs.availableInventory - :quantity, " +
           "fs.updatedAt = CURRENT_TIMESTAMP WHERE fs.id = :id AND fs.availableInventory >= :quantity")
    int decrementInventory(@Param("id") Long id, @Param("quantity") Integer quantity);
    
    /**
     * Release inventory back to available pool.
     */
    @Modifying
    @Query("UPDATE FlashSale fs SET fs.availableInventory = " +
           "CASE WHEN fs.availableInventory + :quantity > fs.totalInventory " +
           "THEN fs.totalInventory ELSE fs.availableInventory + :quantity END, " +
           "fs.updatedAt = CURRENT_TIMESTAMP WHERE fs.id = :id")
    int incrementInventory(@Param("id") Long id, @Param("quantity") Integer quantity);
    
    /**
     * Find flash sales that should be activated (start time has passed).
     */
    @Query("SELECT fs FROM FlashSale fs WHERE fs.status = 'SCHEDULED' " +
           "AND fs.startTime <= :currentTime")
    List<FlashSale> findFlashSalesToActivate(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find flash sales that should be ended (end time has passed or inventory exhausted).
     */
    @Query("SELECT fs FROM FlashSale fs WHERE fs.status = 'ACTIVE' " +
           "AND (fs.endTime <= :currentTime OR fs.availableInventory = 0)")
    List<FlashSale> findFlashSalesToEnd(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Get current inventory count for a flash sale.
     */
    @Query("SELECT fs.availableInventory FROM FlashSale fs WHERE fs.id = :id")
    Optional<Integer> getAvailableInventory(@Param("id") Long id);
    
    /**
     * Check if flash sale exists and is active.
     */
    @Query("SELECT CASE WHEN COUNT(fs) > 0 THEN true ELSE false END " +
           "FROM FlashSale fs WHERE fs.id = :id AND fs.status = 'ACTIVE' " +
           "AND fs.startTime <= :currentTime AND fs.endTime > :currentTime")
    boolean isFlashSaleActive(@Param("id") Long id, @Param("currentTime") LocalDateTime currentTime);
}
