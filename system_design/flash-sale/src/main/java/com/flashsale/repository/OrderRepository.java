package com.flashsale.repository;

import com.flashsale.model.Order;
import com.flashsale.model.OrderStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Order entity operations.
 * 
 * Provides data access methods for order management and reporting.
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    /**
     * Find order by reservation ID.
     */
    Optional<Order> findByReservationId(Long reservationId);
    
    /**
     * Find order by payment ID.
     */
    Optional<Order> findByPaymentId(String paymentId);
    
    /**
     * Find orders by user ID.
     */
    List<Order> findByUserIdOrderByCreatedAtDesc(String userId);
    
    /**
     * Find orders by product ID.
     */
    List<Order> findByProductIdOrderByCreatedAtDesc(String productId);
    
    /**
     * Find orders by status.
     */
    List<Order> findByStatusOrderByCreatedAtDesc(OrderStatus status);
    
    /**
     * Count completed orders for a product within a time range.
     */
    @Query("SELECT COUNT(o) FROM Order o WHERE o.productId = :productId " +
           "AND o.status = 'COMPLETED' AND o.completedAt BETWEEN :startTime AND :endTime")
    long countCompletedOrdersInTimeRange(@Param("productId") String productId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * Get total revenue for a product within a time range.
     */
    @Query("SELECT COALESCE(SUM(o.amount), 0) FROM Order o WHERE o.productId = :productId " +
           "AND o.status = 'COMPLETED' AND o.completedAt BETWEEN :startTime AND :endTime")
    Double getTotalRevenueInTimeRange(@Param("productId") String productId,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * Find pending orders older than specified time.
     */
    @Query("SELECT o FROM Order o WHERE o.status = 'PENDING' " +
           "AND o.createdAt < :cutoffTime")
    List<Order> findStalePendingOrders(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * Get order statistics for a flash sale.
     */
    @Query("SELECT new map(" +
           "COUNT(o) as totalOrders, " +
           "SUM(CASE WHEN o.status = 'COMPLETED' THEN 1 ELSE 0 END) as completedOrders, " +
           "SUM(CASE WHEN o.status = 'FAILED' THEN 1 ELSE 0 END) as failedOrders, " +
           "COALESCE(SUM(CASE WHEN o.status = 'COMPLETED' THEN o.amount ELSE 0 END), 0) as totalRevenue" +
           ") FROM Order o JOIN o.reservation r WHERE r.flashSale.id = :flashSaleId")
    Object getFlashSaleOrderStatistics(@Param("flashSaleId") Long flashSaleId);
}
