package com.flashsale.repository;

import com.flashsale.model.Reservation;
import com.flashsale.model.ReservationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Reservation entity operations.
 * 
 * Handles reservation data access with support for expiration
 * and cleanup operations.
 */
@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {
    
    /**
     * Find active reservation for a user in a specific flash sale.
     */
    @Query("SELECT r FROM Reservation r WHERE r.flashSale.id = :flashSaleId " +
           "AND r.userId = :userId AND r.status = 'RESERVED'")
    Optional<Reservation> findActiveReservation(@Param("flashSaleId") Long flashSaleId, 
                                               @Param("userId") String userId);
    
    /**
     * Check if user already has a reservation for the flash sale.
     */
    @Query("SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END " +
           "FROM Reservation r WHERE r.flashSale.id = :flashSaleId " +
           "AND r.userId = :userId AND r.status IN ('RESERVED', 'PAYMENT_PROCESSING', 'COMPLETED')")
    boolean hasExistingReservation(@Param("flashSaleId") Long flashSaleId, 
                                  @Param("userId") String userId);
    
    /**
     * Find all expired reservations that need cleanup.
     */
    @Query("SELECT r FROM Reservation r WHERE r.status = 'RESERVED' " +
           "AND r.expiresAt <= :currentTime")
    List<Reservation> findExpiredReservations(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Mark expired reservations as expired (bulk operation).
     */
    @Modifying
    @Query("UPDATE Reservation r SET r.status = 'EXPIRED' " +
           "WHERE r.status = 'RESERVED' AND r.expiresAt <= :currentTime")
    int markExpiredReservations(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find reservations by flash sale and status.
     */
    List<Reservation> findByFlashSaleIdAndStatus(Long flashSaleId, ReservationStatus status);
    
    /**
     * Find reservations by user ID.
     */
    List<Reservation> findByUserIdOrderByReservedAtDesc(String userId);
    
    /**
     * Count active reservations for a flash sale.
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.flashSale.id = :flashSaleId " +
           "AND r.status IN ('RESERVED', 'PAYMENT_PROCESSING')")
    long countActiveReservations(@Param("flashSaleId") Long flashSaleId);
    
    /**
     * Find reservation by payment ID.
     */
    Optional<Reservation> findByPaymentId(String paymentId);
    
    /**
     * Get total reserved quantity for a flash sale.
     */
    @Query("SELECT COALESCE(SUM(r.quantity), 0) FROM Reservation r " +
           "WHERE r.flashSale.id = :flashSaleId AND r.status IN ('RESERVED', 'PAYMENT_PROCESSING')")
    Integer getTotalReservedQuantity(@Param("flashSaleId") Long flashSaleId);
    
    /**
     * Delete old completed/expired reservations for cleanup.
     */
    @Modifying
    @Query("DELETE FROM Reservation r WHERE r.status IN ('EXPIRED', 'CANCELLED', 'PAYMENT_FAILED') " +
           "AND r.reservedAt < :cutoffTime")
    int deleteOldReservations(@Param("cutoffTime") LocalDateTime cutoffTime);
}
