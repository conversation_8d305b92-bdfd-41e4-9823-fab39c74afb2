package com.flashsale.service;

import com.flashsale.dto.CreateFlashSaleRequest;
import com.flashsale.model.FlashSale;
import com.flashsale.model.FlashSaleStatus;
import com.flashsale.repository.FlashSaleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing flash sale lifecycle operations.
 * 
 * This service handles creation, activation, and management of flash sales.
 */
@Service
public class FlashSaleManagementService {
    
    private static final Logger logger = LoggerFactory.getLogger(FlashSaleManagementService.class);
    
    @Autowired
    private FlashSaleRepository flashSaleRepository;
    
    @Autowired
    private InventoryService inventoryService;
    
    /**
     * Create a new flash sale.
     */
    @Transactional
    public FlashSale createFlashSale(CreateFlashSaleRequest request) {
        logger.info("Creating flash sale for product: {}", request.getProductId());
        
        // Validate request
        validateCreateRequest(request);
        
        // Create flash sale entity
        FlashSale flashSale = new FlashSale(
            request.getProductId(),
            request.getProductName(),
            request.getTotalInventory(),
            request.getPrice(),
            request.getStartTime(),
            request.getEndTime()
        );
        
        flashSale.setPaymentTimeoutMinutes(request.getPaymentTimeoutMinutes());
        flashSale.setMaxPerUser(request.getMaxPerUser());
        
        // Determine initial status
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(request.getStartTime())) {
            flashSale.setStatus(FlashSaleStatus.ACTIVE);
        } else {
            flashSale.setStatus(FlashSaleStatus.SCHEDULED);
        }
        
        // Save to database
        flashSale = flashSaleRepository.save(flashSale);
        
        // Initialize inventory in Redis if active
        if (flashSale.getStatus() == FlashSaleStatus.ACTIVE) {
            inventoryService.initializeInventory(flashSale.getId(), flashSale.getTotalInventory());
            logger.info("Initialized inventory for active flash sale: {}", flashSale.getId());
        }
        
        logger.info("Created flash sale: {}", flashSale);
        return flashSale;
    }
    
    /**
     * Activate a scheduled flash sale.
     */
    @Transactional
    public boolean activateFlashSale(Long flashSaleId) {
        try {
            FlashSale flashSale = flashSaleRepository.findById(flashSaleId)
                .orElseThrow(() -> new IllegalArgumentException("Flash sale not found: " + flashSaleId));
            
            if (flashSale.getStatus() != FlashSaleStatus.SCHEDULED) {
                logger.warn("Cannot activate flash sale {} - current status: {}", flashSaleId, flashSale.getStatus());
                return false;
            }
            
            // Update status
            flashSale.setStatus(FlashSaleStatus.ACTIVE);
            flashSaleRepository.save(flashSale);
            
            // Initialize inventory in Redis
            inventoryService.initializeInventory(flashSale.getId(), flashSale.getTotalInventory());
            
            logger.info("Activated flash sale: {}", flashSaleId);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to activate flash sale: {}", flashSaleId, e);
            return false;
        }
    }
    
    /**
     * End a flash sale.
     */
    @Transactional
    public boolean endFlashSale(Long flashSaleId) {
        try {
            FlashSale flashSale = flashSaleRepository.findById(flashSaleId)
                .orElseThrow(() -> new IllegalArgumentException("Flash sale not found: " + flashSaleId));
            
            if (flashSale.getStatus() != FlashSaleStatus.ACTIVE) {
                logger.warn("Cannot end flash sale {} - current status: {}", flashSaleId, flashSale.getStatus());
                return false;
            }
            
            // Update status
            flashSale.setStatus(FlashSaleStatus.ENDED);
            flashSaleRepository.save(flashSale);
            
            // Clean up Redis data
            inventoryService.cleanupFlashSaleData(flashSale.getId());
            
            logger.info("Ended flash sale: {}", flashSaleId);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to end flash sale: {}", flashSaleId, e);
            return false;
        }
    }
    
    /**
     * Get all flash sales.
     */
    public List<FlashSale> getAllFlashSales() {
        return flashSaleRepository.findAll();
    }
    
    /**
     * Get flash sales by status.
     */
    public List<FlashSale> getFlashSalesByStatus(FlashSaleStatus status) {
        return flashSaleRepository.findAll().stream()
            .filter(sale -> sale.getStatus() == status)
            .toList();
    }
    
    /**
     * Create a demo flash sale for testing.
     */
    @Transactional
    public FlashSale createDemoFlashSale() {
        logger.info("Creating demo flash sale...");
        
        CreateFlashSaleRequest request = new CreateFlashSaleRequest(
            "DEMO_PHONE_001",
            "Demo Phone Pro Max",
            100,
            new java.math.BigDecimal("999.99"),
            LocalDateTime.now().minusMinutes(5), // Started 5 minutes ago
            LocalDateTime.now().plusHours(1)     // Ends in 1 hour
        );
        
        request.setPaymentTimeoutMinutes(5);
        request.setMaxPerUser(1);
        
        return createFlashSale(request);
    }
    
    /**
     * Validate create flash sale request.
     */
    private void validateCreateRequest(CreateFlashSaleRequest request) {
        if (request.getStartTime().isAfter(request.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }
        
        if (request.getEndTime().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("End time must be in the future");
        }
        
        if (request.getTotalInventory() <= 0) {
            throw new IllegalArgumentException("Total inventory must be positive");
        }
        
        if (request.getPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Price must be non-negative");
        }
    }
}
