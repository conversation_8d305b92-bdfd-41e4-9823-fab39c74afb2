package com.flashsale.service;

import com.flashsale.model.*;
import com.flashsale.repository.FlashSaleRepository;
import com.flashsale.repository.OrderRepository;
import com.flashsale.repository.ReservationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Main service for flash sale operations.
 * 
 * This service orchestrates the entire flash sale flow including:
 * - Inventory reservation
 * - Payment processing
 * - Order creation
 * - Error handling and cleanup
 */
@Service
public class FlashSaleService {
    
    private static final Logger logger = LoggerFactory.getLogger(FlashSaleService.class);
    
    @Autowired
    private FlashSaleRepository flashSaleRepository;
    
    @Autowired
    private ReservationRepository reservationRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private PaymentService paymentService;
    
    /**
     * Attempt to reserve an item for a user in a flash sale.
     * This is the main entry point for the flash sale flow.
     */
    @Transactional
    public ReservationResult reserveItem(Long flashSaleId, String userId, Integer quantity) {
        try {
            logger.info("Processing reservation request for user {} in flash sale {} (quantity: {})", 
                       userId, flashSaleId, quantity);
            
            // 1. Validate flash sale
            FlashSale flashSale = validateFlashSale(flashSaleId);
            
            // 2. Validate user eligibility
            validateUserEligibility(flashSale, userId, quantity);
            
            // 3. Attempt inventory reservation
            boolean reservationSuccess = inventoryService.reserveInventory(flashSaleId, userId, quantity);
            
            if (!reservationSuccess) {
                return new ReservationResult(false, "Insufficient inventory available", null);
            }
            
            // 4. Create reservation record
            LocalDateTime expiresAt = LocalDateTime.now().plusMinutes(flashSale.getPaymentTimeoutMinutes());
            Reservation reservation = new Reservation(flashSale, userId, quantity, expiresAt);
            reservation = reservationRepository.save(reservation);
            
            logger.info("Successfully created reservation {} for user {} in flash sale {}", 
                       reservation.getId(), userId, flashSaleId);
            
            return new ReservationResult(true, "Reservation successful", reservation);
            
        } catch (Exception e) {
            logger.error("Failed to process reservation for user {} in flash sale {}", userId, flashSaleId, e);
            
            // Cleanup: release any reserved inventory
            try {
                inventoryService.releaseInventory(flashSaleId, userId, quantity);
            } catch (Exception cleanupError) {
                logger.error("Failed to cleanup inventory for failed reservation", cleanupError);
            }
            
            return new ReservationResult(false, "Reservation failed: " + e.getMessage(), null);
        }
    }
    
    /**
     * Process payment for a reservation.
     */
    @Transactional
    public CompletableFuture<PaymentResult> processPayment(Long reservationId) {
        try {
            Reservation reservation = reservationRepository.findById(reservationId)
                .orElseThrow(() -> new IllegalArgumentException("Reservation not found: " + reservationId));
            
            if (!reservation.canProcessPayment()) {
                return CompletableFuture.completedFuture(
                    new PaymentResult(false, "Reservation cannot be processed for payment", null)
                );
            }
            
            // Mark reservation as payment processing
            String paymentId = generatePaymentId();
            reservation.markAsPaymentProcessing(paymentId);
            reservationRepository.save(reservation);
            
            // Calculate total amount
            BigDecimal totalAmount = reservation.getFlashSale().getPrice()
                .multiply(BigDecimal.valueOf(reservation.getQuantity()));
            
            // Process payment asynchronously
            CompletableFuture<PaymentService.PaymentResult> paymentFuture = 
                paymentService.processPayment(reservation.getUserId(), totalAmount, reservationId.toString());
            
            // Handle payment result
            return paymentFuture.thenApply(paymentResult -> {
                try {
                    return handlePaymentResult(reservation, paymentResult);
                } catch (Exception e) {
                    logger.error("Failed to handle payment result for reservation {}", reservationId, e);
                    return new PaymentResult(false, "Payment processing error: " + e.getMessage(), null);
                }
            });
            
        } catch (Exception e) {
            logger.error("Failed to process payment for reservation {}", reservationId, e);
            return CompletableFuture.completedFuture(
                new PaymentResult(false, "Payment processing failed: " + e.getMessage(), null)
            );
        }
    }
    
    /**
     * Handle the result of payment processing.
     */
    @Transactional
    public PaymentResult handlePaymentResult(Reservation reservation, PaymentService.PaymentResult paymentResult) {
        try {
            if (paymentResult.isSuccessful()) {
                // Payment successful - create order and complete reservation
                Order order = new Order(reservation, reservation.getFlashSale().getProductId(), 
                                      reservation.getFlashSale().getPrice().multiply(BigDecimal.valueOf(reservation.getQuantity())));
                order.markAsCompleted(paymentResult.getTransactionId());
                orderRepository.save(order);
                
                reservation.markAsCompleted();
                reservationRepository.save(reservation);
                
                logger.info("Payment successful for reservation {}. Order {} created.", 
                           reservation.getId(), order.getId());
                
                return new PaymentResult(true, "Payment completed successfully", order);
                
            } else {
                // Payment failed - release inventory and mark reservation as failed
                reservation.markAsPaymentFailed();
                reservationRepository.save(reservation);
                
                inventoryService.releaseInventory(
                    reservation.getFlashSale().getId(), 
                    reservation.getUserId(), 
                    reservation.getQuantity()
                );
                
                logger.warn("Payment failed for reservation {}. Inventory released.", reservation.getId());
                
                return new PaymentResult(false, "Payment failed: " + paymentResult.getMessage(), null);
            }
        } catch (Exception e) {
            logger.error("Failed to handle payment result for reservation {}", reservation.getId(), e);
            return new PaymentResult(false, "Error handling payment result: " + e.getMessage(), null);
        }
    }
    
    /**
     * Get flash sale status and current inventory.
     */
    public FlashSaleStatus getFlashSaleStatus(Long flashSaleId) {
        FlashSale flashSale = flashSaleRepository.findById(flashSaleId)
            .orElseThrow(() -> new IllegalArgumentException("Flash sale not found: " + flashSaleId));
        
        Integer availableInventory = inventoryService.getAvailableInventory(flashSaleId);
        
        return new FlashSaleStatus(flashSale, availableInventory);
    }
    
    /**
     * Cancel a reservation.
     */
    @Transactional
    public boolean cancelReservation(Long reservationId, String userId) {
        try {
            Reservation reservation = reservationRepository.findById(reservationId)
                .orElseThrow(() -> new IllegalArgumentException("Reservation not found: " + reservationId));
            
            if (!reservation.getUserId().equals(userId)) {
                throw new IllegalArgumentException("Reservation does not belong to user");
            }
            
            if (reservation.getStatus() != ReservationStatus.RESERVED) {
                return false; // Cannot cancel non-reserved reservations
            }
            
            reservation.markAsCancelled();
            reservationRepository.save(reservation);
            
            // Release inventory
            inventoryService.releaseInventory(
                reservation.getFlashSale().getId(),
                reservation.getUserId(),
                reservation.getQuantity()
            );
            
            logger.info("Cancelled reservation {} for user {}", reservationId, userId);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to cancel reservation {} for user {}", reservationId, userId, e);
            return false;
        }
    }
    
    /**
     * Validate flash sale eligibility.
     */
    private FlashSale validateFlashSale(Long flashSaleId) {
        FlashSale flashSale = flashSaleRepository.findById(flashSaleId)
            .orElseThrow(() -> new IllegalArgumentException("Flash sale not found: " + flashSaleId));
        
        if (!flashSale.isActive()) {
            throw new IllegalStateException("Flash sale is not active");
        }
        
        if (!flashSale.hasInventoryAvailable()) {
            throw new IllegalStateException("No inventory available");
        }
        
        return flashSale;
    }
    
    /**
     * Validate user eligibility for reservation.
     */
    private void validateUserEligibility(FlashSale flashSale, String userId, Integer quantity) {
        // Check if user already has a reservation
        if (reservationRepository.hasExistingReservation(flashSale.getId(), userId)) {
            throw new IllegalStateException("User already has a reservation for this flash sale");
        }
        
        // Check quantity limits
        if (quantity > flashSale.getMaxPerUser()) {
            throw new IllegalArgumentException("Quantity exceeds maximum allowed per user");
        }
        
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity must be positive");
        }
    }
    
    /**
     * Generate unique payment ID.
     */
    private String generatePaymentId() {
        return "PAY_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    // Result classes
    public static class ReservationResult {
        private final boolean success;
        private final String message;
        private final Reservation reservation;
        
        public ReservationResult(boolean success, String message, Reservation reservation) {
            this.success = success;
            this.message = message;
            this.reservation = reservation;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Reservation getReservation() { return reservation; }
    }
    
    public static class PaymentResult {
        private final boolean success;
        private final String message;
        private final Order order;
        
        public PaymentResult(boolean success, String message, Order order) {
            this.success = success;
            this.message = message;
            this.order = order;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Order getOrder() { return order; }
    }
    
    public static class FlashSaleStatus {
        private final FlashSale flashSale;
        private final Integer availableInventory;
        
        public FlashSaleStatus(FlashSale flashSale, Integer availableInventory) {
            this.flashSale = flashSale;
            this.availableInventory = availableInventory;
        }
        
        public FlashSale getFlashSale() { return flashSale; }
        public Integer getAvailableInventory() { return availableInventory; }
    }
}
