package com.flashsale.service;

import com.flashsale.model.Reservation;
import com.flashsale.repository.ReservationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for cleaning up expired reservations and releasing inventory.
 * 
 * This service runs background tasks to:
 * - Mark expired reservations as expired
 * - Release inventory back to the pool
 * - Clean up old reservation data
 */
@Service
public class CleanupService {
    
    private static final Logger logger = LoggerFactory.getLogger(CleanupService.class);
    
    @Autowired
    private ReservationRepository reservationRepository;
    
    @Autowired
    private InventoryService inventoryService;
    
    /**
     * Clean up expired reservations every 30 seconds.
     * This ensures that expired reservations are quickly processed
     * and inventory is released back to the pool.
     */
    @Scheduled(fixedDelay = 30000) // 30 seconds
    @Async("cleanupExecutor")
    @Transactional
    public void cleanupExpiredReservations() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // Find all expired reservations
            List<Reservation> expiredReservations = reservationRepository.findExpiredReservations(now);
            
            if (!expiredReservations.isEmpty()) {
                logger.info("Found {} expired reservations to cleanup", expiredReservations.size());
                
                for (Reservation reservation : expiredReservations) {
                    try {
                        // Release inventory back to the pool
                        inventoryService.releaseInventory(
                            reservation.getFlashSale().getId(),
                            reservation.getUserId(),
                            reservation.getQuantity()
                        );
                        
                        // Mark reservation as expired
                        reservation.markAsExpired();
                        reservationRepository.save(reservation);
                        
                        logger.debug("Cleaned up expired reservation {} for user {}", 
                                   reservation.getId(), reservation.getUserId());
                        
                    } catch (Exception e) {
                        logger.error("Failed to cleanup reservation {}", reservation.getId(), e);
                    }
                }
                
                logger.info("Successfully cleaned up {} expired reservations", expiredReservations.size());
            }
            
        } catch (Exception e) {
            logger.error("Error during expired reservations cleanup", e);
        }
    }
    
    /**
     * Clean up old reservation data every hour.
     * This removes old completed/expired/cancelled reservations
     * to prevent database bloat.
     */
    @Scheduled(fixedDelay = 3600000) // 1 hour
    @Async("cleanupExecutor")
    @Transactional
    public void cleanupOldReservations() {
        try {
            // Delete reservations older than 24 hours
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
            
            int deletedCount = reservationRepository.deleteOldReservations(cutoffTime);
            
            if (deletedCount > 0) {
                logger.info("Deleted {} old reservations", deletedCount);
            }
            
        } catch (Exception e) {
            logger.error("Error during old reservations cleanup", e);
        }
    }
    
    /**
     * Synchronize inventory between Redis and database every 5 minutes.
     * This ensures eventual consistency between the two data stores.
     */
    @Scheduled(fixedDelay = 300000) // 5 minutes
    @Async("cleanupExecutor")
    public void synchronizeInventory() {
        try {
            // This would typically iterate through all active flash sales
            // For now, we'll log that the sync is running
            logger.debug("Running inventory synchronization");
            
            // In a real implementation, you would:
            // 1. Get all active flash sales
            // 2. For each sale, call inventoryService.synchronizeInventory(saleId)
            // 3. Handle any discrepancies found
            
        } catch (Exception e) {
            logger.error("Error during inventory synchronization", e);
        }
    }
    
    /**
     * Manual cleanup method that can be called on-demand.
     */
    public void performManualCleanup() {
        logger.info("Starting manual cleanup process");
        
        try {
            cleanupExpiredReservations();
            cleanupOldReservations();
            synchronizeInventory();
            
            logger.info("Manual cleanup completed successfully");
            
        } catch (Exception e) {
            logger.error("Error during manual cleanup", e);
        }
    }
}
