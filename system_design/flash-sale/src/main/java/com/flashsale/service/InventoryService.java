package com.flashsale.service;

import com.flashsale.model.FlashSale;
import com.flashsale.repository.FlashSaleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * Service for managing inventory operations with Redis caching.
 * 
 * This service implements a dual-storage approach:
 * - Redis for fast, atomic inventory operations
 * - PostgreSQL for persistent storage and consistency
 * 
 * The Redis operations are atomic and prevent race conditions
 * during high-concurrency scenarios.
 */
@Service
public class InventoryService {
    
    private static final Logger logger = LoggerFactory.getLogger(InventoryService.class);
    private static final String INVENTORY_KEY_PREFIX = "flash_sale:inventory:";
    private static final String RESERVATION_KEY_PREFIX = "flash_sale:reservation:";
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private FlashSaleRepository flashSaleRepository;
    
    /**
     * Initialize inventory in Redis for a flash sale.
     * This should be called when a flash sale becomes active.
     */
    public void initializeInventory(Long flashSaleId, Integer totalInventory) {
        String key = INVENTORY_KEY_PREFIX + flashSaleId;
        redisTemplate.opsForValue().set(key, totalInventory);
        
        // Set expiration to prevent memory leaks (24 hours)
        redisTemplate.expire(key, 24, TimeUnit.HOURS);
        
        logger.info("Initialized inventory for flash sale {} with {} items", flashSaleId, totalInventory);
    }
    
    /**
     * Attempt to reserve inventory atomically.
     * Returns true if reservation was successful, false if insufficient inventory.
     */
    public boolean reserveInventory(Long flashSaleId, String userId, Integer quantity) {
        String inventoryKey = INVENTORY_KEY_PREFIX + flashSaleId;
        String reservationKey = RESERVATION_KEY_PREFIX + flashSaleId + ":" + userId;
        
        // Check if user already has a reservation
        if (Boolean.TRUE.equals(redisTemplate.hasKey(reservationKey))) {
            logger.warn("User {} already has a reservation for flash sale {}", userId, flashSaleId);
            return false;
        }
        
        // Atomic decrement operation
        Long remainingInventory = redisTemplate.opsForValue().decrement(inventoryKey, quantity);
        
        if (remainingInventory != null && remainingInventory >= 0) {
            // Successful reservation - store user reservation with TTL
            redisTemplate.opsForValue().set(reservationKey, quantity, 5, TimeUnit.MINUTES);
            
            logger.info("Reserved {} items for user {} in flash sale {}. Remaining: {}", 
                       quantity, userId, flashSaleId, remainingInventory);
            return true;
        } else {
            // Insufficient inventory - rollback the decrement
            redisTemplate.opsForValue().increment(inventoryKey, quantity);
            logger.warn("Insufficient inventory for user {} in flash sale {}. Requested: {}", 
                       userId, flashSaleId, quantity);
            return false;
        }
    }
    
    /**
     * Release reserved inventory back to the pool.
     * This is called when a reservation expires or payment fails.
     */
    public void releaseInventory(Long flashSaleId, String userId, Integer quantity) {
        String inventoryKey = INVENTORY_KEY_PREFIX + flashSaleId;
        String reservationKey = RESERVATION_KEY_PREFIX + flashSaleId + ":" + userId;
        
        // Remove user reservation
        redisTemplate.delete(reservationKey);
        
        // Add inventory back
        Long newInventory = redisTemplate.opsForValue().increment(inventoryKey, quantity);
        
        logger.info("Released {} items for user {} in flash sale {}. New inventory: {}", 
                   quantity, userId, flashSaleId, newInventory);
    }
    
    /**
     * Get current available inventory from Redis.
     */
    public Integer getAvailableInventory(Long flashSaleId) {
        String key = INVENTORY_KEY_PREFIX + flashSaleId;
        Object inventory = redisTemplate.opsForValue().get(key);
        return inventory != null ? (Integer) inventory : 0;
    }
    
    /**
     * Check if user has an active reservation.
     */
    public boolean hasActiveReservation(Long flashSaleId, String userId) {
        String reservationKey = RESERVATION_KEY_PREFIX + flashSaleId + ":" + userId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(reservationKey));
    }
    
    /**
     * Synchronize Redis inventory with database.
     * This method ensures eventual consistency between Redis and PostgreSQL.
     */
    @Transactional
    public void synchronizeInventory(Long flashSaleId) {
        try {
            FlashSale flashSale = flashSaleRepository.findById(flashSaleId)
                .orElseThrow(() -> new IllegalArgumentException("Flash sale not found: " + flashSaleId));
            
            Integer redisInventory = getAvailableInventory(flashSaleId);
            Integer dbInventory = flashSale.getAvailableInventory();
            
            if (!redisInventory.equals(dbInventory)) {
                logger.warn("Inventory mismatch for flash sale {}. Redis: {}, DB: {}", 
                           flashSaleId, redisInventory, dbInventory);
                
                // Update database to match Redis (Redis is source of truth for active sales)
                flashSale.setAvailableInventory(redisInventory);
                flashSaleRepository.save(flashSale);
                
                logger.info("Synchronized inventory for flash sale {} to {}", flashSaleId, redisInventory);
            }
        } catch (Exception e) {
            logger.error("Failed to synchronize inventory for flash sale {}", flashSaleId, e);
        }
    }
    
    /**
     * Clean up Redis data for completed flash sales.
     */
    public void cleanupFlashSaleData(Long flashSaleId) {
        String inventoryKey = INVENTORY_KEY_PREFIX + flashSaleId;
        String reservationPattern = RESERVATION_KEY_PREFIX + flashSaleId + ":*";
        
        // Delete inventory key
        redisTemplate.delete(inventoryKey);
        
        // Delete all reservation keys for this flash sale
        redisTemplate.delete(redisTemplate.keys(reservationPattern));
        
        logger.info("Cleaned up Redis data for flash sale {}", flashSaleId);
    }
    
    /**
     * Get reservation details for a user.
     */
    public Integer getUserReservationQuantity(Long flashSaleId, String userId) {
        String reservationKey = RESERVATION_KEY_PREFIX + flashSaleId + ":" + userId;
        Object quantity = redisTemplate.opsForValue().get(reservationKey);
        return quantity != null ? (Integer) quantity : 0;
    }
    
    /**
     * Extend reservation TTL (e.g., when payment processing starts).
     */
    public void extendReservation(Long flashSaleId, String userId, long additionalMinutes) {
        String reservationKey = RESERVATION_KEY_PREFIX + flashSaleId + ":" + userId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(reservationKey))) {
            redisTemplate.expire(reservationKey, additionalMinutes, TimeUnit.MINUTES);
            logger.info("Extended reservation for user {} in flash sale {} by {} minutes", 
                       userId, flashSaleId, additionalMinutes);
        }
    }
}
