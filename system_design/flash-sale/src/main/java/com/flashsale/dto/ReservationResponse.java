package com.flashsale.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * Response DTO for item reservation results.
 */
public class ReservationResponse {
    
    private Long reservationId;
    private String userId;
    private Integer quantity;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime expiresAt;
    
    private String message;
    
    // Constructors
    public ReservationResponse() {}
    
    public ReservationResponse(Long reservationId, String userId, Integer quantity, 
                              LocalDateTime expiresAt, String message) {
        this.reservationId = reservationId;
        this.userId = userId;
        this.quantity = quantity;
        this.expiresAt = expiresAt;
        this.message = message;
    }
    
    // Getters and Setters
    public Long getReservationId() {
        return reservationId;
    }
    
    public void setReservationId(Long reservationId) {
        this.reservationId = reservationId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "ReservationResponse{" +
                "reservationId=" + reservationId +
                ", userId='" + userId + '\'' +
                ", quantity=" + quantity +
                ", expiresAt=" + expiresAt +
                ", message='" + message + '\'' +
                '}';
    }
}
