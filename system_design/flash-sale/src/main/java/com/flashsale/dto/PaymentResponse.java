package com.flashsale.dto;

import java.math.BigDecimal;

/**
 * Response DTO for payment processing results.
 */
public class PaymentResponse {
    
    private boolean success;
    private Long orderId;
    private String paymentId;
    private BigDecimal amount;
    private String message;
    
    // Constructors
    public PaymentResponse() {}
    
    public PaymentResponse(boolean success, Long orderId, String paymentId, 
                          BigDecimal amount, String message) {
        this.success = success;
        this.orderId = orderId;
        this.paymentId = paymentId;
        this.amount = amount;
        this.message = message;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public String getPaymentId() {
        return paymentId;
    }
    
    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "PaymentResponse{" +
                "success=" + success +
                ", orderId=" + orderId +
                ", paymentId='" + paymentId + '\'' +
                ", amount=" + amount +
                ", message='" + message + '\'' +
                '}';
    }
}
