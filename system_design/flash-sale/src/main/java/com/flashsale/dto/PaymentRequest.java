package com.flashsale.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * Request DTO for payment processing.
 */
public class PaymentRequest {
    
    @NotBlank(message = "Payment method is required")
    private String paymentMethod;
    
    private String cardToken;
    private String billingAddress;
    
    // Constructors
    public PaymentRequest() {}
    
    public PaymentRequest(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public PaymentRequest(String paymentMethod, String cardToken, String billingAddress) {
        this.paymentMethod = paymentMethod;
        this.cardToken = cardToken;
        this.billingAddress = billingAddress;
    }
    
    // Getters and Setters
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getCardToken() {
        return cardToken;
    }
    
    public void setCardToken(String cardToken) {
        this.cardToken = cardToken;
    }
    
    public String getBillingAddress() {
        return billingAddress;
    }
    
    public void setBillingAddress(String billingAddress) {
        this.billingAddress = billingAddress;
    }
    
    @Override
    public String toString() {
        return "PaymentRequest{" +
                "paymentMethod='" + paymentMethod + '\'' +
                ", cardToken='" + (cardToken != null ? "***" : null) + '\'' +
                ", billingAddress='" + billingAddress + '\'' +
                '}';
    }
}
