package com.flashsale.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Request DTO for creating a new flash sale.
 */
public class CreateFlashSaleRequest {
    
    @NotBlank(message = "Product ID is required")
    private String productId;
    
    @NotBlank(message = "Product name is required")
    private String productName;
    
    @NotNull(message = "Total inventory is required")
    @Min(value = 1, message = "Total inventory must be at least 1")
    private Integer totalInventory;
    
    @NotNull(message = "Price is required")
    @Min(value = 0, message = "Price must be non-negative")
    private BigDecimal price;
    
    @NotNull(message = "Start time is required")
    private LocalDateTime startTime;
    
    @NotNull(message = "End time is required")
    private LocalDateTime endTime;
    
    @Min(value = 1, message = "Payment timeout must be at least 1 minute")
    private Integer paymentTimeoutMinutes = 5;
    
    @Min(value = 1, message = "Max per user must be at least 1")
    private Integer maxPerUser = 1;
    
    // Constructors
    public CreateFlashSaleRequest() {}
    
    public CreateFlashSaleRequest(String productId, String productName, Integer totalInventory,
                                 BigDecimal price, LocalDateTime startTime, LocalDateTime endTime) {
        this.productId = productId;
        this.productName = productName;
        this.totalInventory = totalInventory;
        this.price = price;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    // Getters and Setters
    public String getProductId() {
        return productId;
    }
    
    public void setProductId(String productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Integer getTotalInventory() {
        return totalInventory;
    }
    
    public void setTotalInventory(Integer totalInventory) {
        this.totalInventory = totalInventory;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Integer getPaymentTimeoutMinutes() {
        return paymentTimeoutMinutes;
    }
    
    public void setPaymentTimeoutMinutes(Integer paymentTimeoutMinutes) {
        this.paymentTimeoutMinutes = paymentTimeoutMinutes;
    }
    
    public Integer getMaxPerUser() {
        return maxPerUser;
    }
    
    public void setMaxPerUser(Integer maxPerUser) {
        this.maxPerUser = maxPerUser;
    }
    
    @Override
    public String toString() {
        return "CreateFlashSaleRequest{" +
                "productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", totalInventory=" + totalInventory +
                ", price=" + price +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", paymentTimeoutMinutes=" + paymentTimeoutMinutes +
                ", maxPerUser=" + maxPerUser +
                '}';
    }
}
