package com.flashsale.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Response DTO for flash sale status information.
 */
public class FlashSaleStatusResponse {
    
    private Long id;
    private String productId;
    private String productName;
    private BigDecimal price;
    private Integer totalInventory;
    private Integer availableInventory;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status;
    private Integer paymentTimeoutMinutes;
    private Integer maxPerUser;
    
    // Constructors
    public FlashSaleStatusResponse() {}
    
    public FlashSaleStatusResponse(Long id, String productId, String productName, BigDecimal price,
                                  Integer totalInventory, Integer availableInventory,
                                  LocalDateTime startTime, LocalDateTime endTime,
                                  String status, Integer paymentTimeoutMinutes,
                                  Integer maxPerUser) {
        this.id = id;
        this.productId = productId;
        this.productName = productName;
        this.price = price;
        this.totalInventory = totalInventory;
        this.availableInventory = availableInventory;
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
        this.paymentTimeoutMinutes = paymentTimeoutMinutes;
        this.maxPerUser = maxPerUser;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getProductId() {
        return productId;
    }
    
    public void setProductId(String productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getTotalInventory() {
        return totalInventory;
    }
    
    public void setTotalInventory(Integer totalInventory) {
        this.totalInventory = totalInventory;
    }
    
    public Integer getAvailableInventory() {
        return availableInventory;
    }
    
    public void setAvailableInventory(Integer availableInventory) {
        this.availableInventory = availableInventory;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getPaymentTimeoutMinutes() {
        return paymentTimeoutMinutes;
    }
    
    public void setPaymentTimeoutMinutes(Integer paymentTimeoutMinutes) {
        this.paymentTimeoutMinutes = paymentTimeoutMinutes;
    }
    
    public Integer getMaxPerUser() {
        return maxPerUser;
    }
    
    public void setMaxPerUser(Integer maxPerUser) {
        this.maxPerUser = maxPerUser;
    }
    
    @Override
    public String toString() {
        return "FlashSaleStatusResponse{" +
                "id=" + id +
                ", productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", price=" + price +
                ", totalInventory=" + totalInventory +
                ", availableInventory=" + availableInventory +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", status=" + status +
                ", paymentTimeoutMinutes=" + paymentTimeoutMinutes +
                ", maxPerUser=" + maxPerUser +
                '}';
    }
}
