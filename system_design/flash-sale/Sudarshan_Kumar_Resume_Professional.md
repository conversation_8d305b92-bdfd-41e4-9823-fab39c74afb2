# **<PERSON><PERSON><PERSON>**
**Senior Software Development Engineer**

📍 Bangalore, India | 📞 (+91) 9635149797 | ✉️ <EMAIL>  
🔗 [LinkedIn](https://www.linkedin.com/in/sudar<PERSON>-kumar-65643530) | 💻 [GitHub](https://github.com/sud-code)

---

## **PROFESSIONAL SUMMARY**

Results-driven Senior Software Development Engineer with 8+ years of expertise in designing and implementing secure, scalable cloud-native applications. Proven track record of delivering high-impact solutions that enhance security, improve user experience, and drive business growth. Multiple award winner with demonstrated innovation in AI/ML integration, infrastructure optimization, and developer productivity tools.

---

## **TECHNICAL SKILLS**

**Programming Languages:** Java, Python, JavaScript, React  
**Cloud Technologies:** AWS Lambda, API Gateway, DynamoDB, ECS/Fargate, SNS, SQS  
**Security:** Transitive Authentication, MONS, Fine-Grained Access Control, IAM  
**Architecture:** Microservices, Event-driven design, RESTful APIs  
**AI/ML:** Large Language Models (LLM), Natural Language Processing, Code Analysis  
**Tools & Frameworks:** Dagger, React+Redux, CDK, Katal  
**Performance Optimization:** Lambda cold start optimization, caching strategies  

---

## **PROFESSIONAL EXPERIENCE**

### **Amazon | Senior Software Development Engineer | June 2016 – Present**

#### **Intelligent Router Architecture**

**Intelligent Router and Response Moderator for Delphi/Sapien (2024)**
- Architected a scalable, event-driven system to route user queries to appropriate solution providers based on intent classification
- Designed a secure message passing architecture using SNS/SQS for asynchronous communication between services
- Implemented guardrails to prevent malicious content from reaching end users
- Created a flexible system supporting both synchronous and asynchronous solution providers with minimal latency overhead

**Delphi Chat Authorization Framework (2025)**
- Designed and implemented a comprehensive security framework enabling vendor self-service capabilities while ensuring robust data protection
- Developed a dual-layer authorization system combining action-based and data-level controls using Amazon's MONS framework and Transitive Authentication
- Implemented secure token propagation through service chains while maintaining compliance with Digital Markets Act requirements
- Enhanced security posture by preventing unauthorized data access while maintaining performance standards

#### **Business Intelligence Solutions**

**Business Insights Dashboard (BID) (2022-2023)**
- Led the design and implementation of an interactive dashboard enabling vendors to track performance metrics, define growth targets, and gain business insights
- Architected a scalable backend using AWS Lambda and API Gateway to handle complex data queries
- Implemented performance optimizations reducing API latency from 15 seconds to under 6 milliseconds
- Developed secure data access controls ensuring vendors could only access their authorized data
- Created a phased launch plan to incrementally deliver value while validating approach

#### **Performance Optimization**

**Lambda Cold Start Optimization (2023)**
- Reduced Lambda cold start times by over 80% through implementation of multiple optimization techniques
- Implemented Dagger for compile-time dependency injection, reducing initialization overhead
- Optimized JVM settings using tiered compilation and memory allocation strategies
- Implemented strategic caching and parallel processing to improve response times
- Documented best practices for Lambda optimization that were adopted across multiple teams

---

## **KEY ACHIEVEMENTS & INNOVATIONS**

### **🏆 LLM-Based Code Review Analyzer (2024)**
**Innovation Award Winner - Organization Level**
- Developed an intelligent code review system leveraging Large Language Models to automatically analyze pull requests and generate contextual comments
- Integrated natural language processing to understand code semantics, identify potential bugs, security vulnerabilities, and suggest improvements
- Reduced manual code review time by 60% while improving code quality consistency across teams
- Implemented machine learning pipeline to continuously improve comment relevance based on developer feedback
- **Impact:** Enhanced developer productivity and code quality across 15+ engineering teams

### **🏆 InTech Demo Day Award Winner (2024)**
**LLM Code Review Analyzer Presentation**
- Selected as winner among 50+ innovative projects at Amazon's InTech Demo Day
- Presented AI-driven solution to senior leadership, demonstrating technical innovation and business impact
- Showcased integration of cutting-edge LLM technology with existing development workflows
- **Recognition:** Highlighted as exemplary innovation in developer tools and AI integration

### **🛠️ Operational Excellence (OE) HeatMap Tool (2023)**
**Developer Productivity Innovation**
- Created an automated visualization tool for operational metrics and system health monitoring
- Implemented real-time data aggregation from multiple AWS services and internal monitoring systems
- Developed intuitive dashboard with color-coded heat maps for quick identification of system bottlenecks
- **Impact:** Reduced on-call manual reporting work from 30 minutes to under 5 minutes per weekly report
- **Adoption:** Tool adopted by 8 teams across the organization for operational monitoring

### **🏆 Dolphin Tank Winner - "Infrastructure Made Easy" (2023)**
**Internal Innovation Competition**
- Won Amazon's internal "Shark Tank" style competition for infrastructure automation solution
- Presented comprehensive platform for simplifying cloud infrastructure deployment and management
- Developed self-service infrastructure provisioning tools reducing deployment time by 75%
- **Business Impact:** Enabled faster time-to-market for new services and reduced operational overhead

### **🏆 Trident Team Award Winner (2022)**
**Cross-Functional Excellence Recognition**
- Recognized for exceptional collaboration and delivery excellence in cross-team initiatives
- Led integration efforts between multiple service teams for Business Insights Dashboard project
- Demonstrated leadership in coordinating complex technical requirements across diverse stakeholders
- **Result:** Successful delivery of $967M GMS impact project ahead of schedule

---

## **QUANTIFIED IMPACT**

- **Performance:** Reduced API latency from 15 seconds to under 6 milliseconds (99.96% improvement)
- **Business Growth:** Created business intelligence dashboard driving **$967M in 3-year GMS** for EU vendors
- **Developer Productivity:** Reduced code review time by 60% through AI automation
- **Operational Efficiency:** Cut manual reporting time from 30 minutes to 5 minutes (83% reduction)
- **Infrastructure:** Achieved 80% reduction in Lambda cold start times
- **Team Impact:** Solutions adopted across 15+ engineering teams organization-wide

---

## **EDUCATION**

**Integrated Masters in Mathematics and Computing**  
Indian Institute of Technology (IIT) Kharagpur | Class of 2015

---

## **CORE COMPETENCIES**

✅ **Innovation Leadership:** Multiple award-winning solutions with measurable business impact  
✅ **AI/ML Integration:** Practical application of LLMs and machine learning in enterprise systems  
✅ **Cloud Architecture:** Scalable, secure, and cost-effective AWS solutions  
✅ **Performance Engineering:** Proven track record of significant latency and efficiency improvements  
✅ **Security Design:** Implementation of robust authorization and data protection frameworks  
✅ **Cross-functional Leadership:** Successfully leading complex projects across multiple teams  

---

*This resume showcases a proven track record of technical excellence, innovation, and business impact, with multiple recognitions for outstanding contributions to software engineering and developer productivity.*
