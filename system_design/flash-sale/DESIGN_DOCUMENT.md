# Flash Sale System Design Document

## Problem Statement

Design a flash sale system that supports the sale of fixed inventory (1000 XPhones) in a very short time with the following constraints:
- Only 1000 users can add items to cart
- Each user can buy only one item
- 5-minute payment window
- Failed payments release inventory back to pool
- High throughput and availability requirements

## System Architecture

### High-Level Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │   CDN/Cache     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Flash Sale      │    │ Inventory       │    │ Payment         │
│ Service         │    │ Service         │    │ Service         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Redis Cache     │    │ PostgreSQL      │    │ Message Queue   │
│ (Inventory)     │    │ (Persistent)    │    │ (Events)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Services

#### 1. Flash Sale Service
- **Responsibility**: Orchestrates the flash sale flow
- **Key Functions**:
  - Validate sale timing and user eligibility
  - Coordinate with inventory and payment services
  - Handle cart reservations and timeouts

#### 2. Inventory Service
- **Responsibility**: Manages product inventory and reservations
- **Key Functions**:
  - Track available inventory in real-time
  - Handle inventory reservations with TTL
  - Release expired reservations

#### 3. Payment Service
- **Responsibility**: Process payments and handle failures
- **Key Functions**:
  - Simulate payment processing
  - Handle payment timeouts and failures
  - Trigger inventory release on failure

## Database Design

### PostgreSQL Schema

```sql
-- Flash Sales table
CREATE TABLE flash_sales (
    id BIGSERIAL PRIMARY KEY,
    product_id VARCHAR(100) NOT NULL,
    total_inventory INTEGER NOT NULL,
    available_inventory INTEGER NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    payment_timeout_minutes INTEGER DEFAULT 5,
    max_per_user INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'SCHEDULED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reservations table
CREATE TABLE reservations (
    id BIGSERIAL PRIMARY KEY,
    flash_sale_id BIGINT REFERENCES flash_sales(id),
    user_id VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'RESERVED',
    reserved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    payment_id VARCHAR(100),
    UNIQUE(flash_sale_id, user_id)
);

-- Orders table
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    reservation_id BIGINT REFERENCES reservations(id),
    user_id VARCHAR(100) NOT NULL,
    product_id VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

### Redis Schema

```
# Inventory counter (atomic operations)
flash_sale:{sale_id}:inventory -> available_count

# User reservations with TTL
flash_sale:{sale_id}:user:{user_id} -> reservation_data (TTL: 5 minutes)

# Sale status cache
flash_sale:{sale_id}:status -> sale_metadata
```

## API Design

### REST Endpoints

```
POST /api/v1/flash-sales/{saleId}/reserve
- Reserve inventory for user
- Returns: reservation_id, expires_at

POST /api/v1/reservations/{reservationId}/payment
- Process payment for reservation
- Returns: order_id, status

GET /api/v1/flash-sales/{saleId}/status
- Get current sale status and inventory
- Returns: available_inventory, sale_status

DELETE /api/v1/reservations/{reservationId}
- Cancel reservation (manual release)
- Returns: success status
```

## Critical Design Decisions

### 1. Inventory Management Strategy

**Decision**: Use Redis atomic operations for inventory tracking with PostgreSQL as source of truth

**Rationale**:
- Redis DECR operation is atomic and fast
- Prevents overselling through race conditions
- PostgreSQL provides durability and consistency
- Eventual consistency between Redis and PostgreSQL is acceptable

**Trade-off**: Slight complexity in maintaining dual storage, but significant performance gain

### 2. Reservation System with TTL

**Decision**: Implement time-bound reservations with automatic expiry

**Implementation**:
- Redis TTL for automatic cleanup
- Background job for PostgreSQL cleanup
- Optimistic approach - reserve first, validate later

**Benefits**:
- Prevents inventory deadlock
- Automatic cleanup reduces manual intervention
- Better user experience with clear timeouts

### 3. Payment Processing

**Decision**: Asynchronous payment with callback mechanism

**Flow**:
1. Create payment intent
2. Return to user immediately
3. Process payment asynchronously
4. Handle success/failure via callbacks

**Benefits**:
- Non-blocking user experience
- Better error handling
- Scalable payment processing

## Scalability Considerations

### Horizontal Scaling

1. **Stateless Services**: All services are stateless and can be scaled horizontally
2. **Database Sharding**: Partition by flash_sale_id for better distribution
3. **Redis Clustering**: Use Redis cluster for high availability
4. **Load Balancing**: Distribute traffic across multiple instances

### Vertical Scaling

1. **Database Optimization**: Proper indexing and query optimization
2. **Connection Pooling**: Efficient database connection management
3. **Caching Strategy**: Multi-layer caching (Redis, Application, CDN)

### Auto-scaling Triggers

- CPU utilization > 70%
- Memory utilization > 80%
- Request queue length > 100
- Response time > 500ms

## Performance Optimizations

### 1. Pre-warming Strategy
- Cache sale data before start time
- Pre-allocate database connections
- Warm up application instances

### 2. Circuit Breaker Pattern
- Prevent cascade failures
- Graceful degradation
- Fast failure detection

### 3. Rate Limiting
- Per-user rate limits
- Global rate limits
- Adaptive rate limiting based on system load

## Monitoring and Observability

### Key Metrics
- Inventory depletion rate
- Reservation success rate
- Payment completion rate
- System latency (p95, p99)
- Error rates by service

### Alerting
- Inventory exhaustion
- High error rates
- Payment processing delays
- System overload conditions

## Cost Optimization

### Infrastructure Costs
- **Auto-scaling**: Scale down during non-peak hours
- **Spot Instances**: Use for non-critical workloads
- **Reserved Instances**: For baseline capacity

### Operational Costs
- **Monitoring**: Use cost-effective monitoring solutions
- **Logging**: Implement log retention policies
- **Storage**: Archive old data to cheaper storage tiers

## Failure Scenarios and Mitigation

### 1. Database Failure
- **Mitigation**: Read replicas, automatic failover
- **Recovery**: Point-in-time recovery, backup restoration

### 2. Redis Failure
- **Mitigation**: Redis clustering, persistence enabled
- **Recovery**: Rebuild cache from PostgreSQL

### 3. Payment Service Failure
- **Mitigation**: Circuit breaker, retry mechanism
- **Recovery**: Manual payment processing, inventory adjustment

### 4. Network Partitions
- **Mitigation**: Multi-AZ deployment, health checks
- **Recovery**: Automatic traffic rerouting

## Security Considerations

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Rate limiting per user

### Data Protection
- Encryption at rest and in transit
- PII data anonymization
- Audit logging

### DDoS Protection
- CDN-based protection
- Application-level rate limiting
- IP-based blocking

## Capacity Planning

### Expected Load
- **Peak Concurrent Users**: 100,000
- **Requests per Second**: 50,000
- **Database Connections**: 1,000
- **Redis Memory**: 16GB

### Resource Allocation
- **Application Servers**: 20 instances (4 vCPU, 8GB RAM each)
- **Database**: Primary + 2 read replicas (8 vCPU, 32GB RAM)
- **Redis**: 3-node cluster (4 vCPU, 16GB RAM each)
- **Load Balancers**: 2 instances for high availability

## Future Enhancements

1. **Machine Learning**: Demand prediction and dynamic pricing
2. **Real-time Analytics**: Live dashboards for business metrics
3. **Multi-region**: Global deployment for reduced latency
4. **Advanced Queuing**: Virtual queuing system for better UX
5. **Blockchain**: Immutable audit trail for high-value items

## Conclusion

This design prioritizes consistency, performance, and scalability while maintaining simplicity. The key innovation is the dual-storage approach for inventory management, which provides both speed and reliability. The system can handle high concurrent load while ensuring data consistency and providing excellent user experience.

## Implementation Notes

The prototype implementation includes:

1. **Spring Boot Application**: RESTful APIs with proper error handling
2. **JPA Entities**: Well-designed domain models with business logic
3. **Redis Integration**: Fast inventory tracking and caching
4. **Payment Simulation**: Configurable success/failure rates
5. **Automatic Cleanup**: Background jobs for expired reservations
6. **Comprehensive Testing**: Unit and integration tests
7. **Monitoring**: Health checks and metrics endpoints

### Key Features Implemented:
- Atomic inventory operations using Redis
- Time-bound reservations with automatic expiry
- Concurrent access handling with proper locking
- Payment simulation with configurable failure rates
- Comprehensive error handling and validation
- Background cleanup processes
- RESTful API design with proper HTTP status codes
