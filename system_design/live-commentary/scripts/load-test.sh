#!/bin/bash

# Load Test Script for Cricket Commentary Service
# Tests the system's ability to handle high concurrent load

set -e

# Configuration
BASE_URL="http://localhost:8080"
CONCURRENT_USERS=100
REQUESTS_PER_USER=50
TOTAL_REQUESTS=$((CONCURRENT_USERS * REQUESTS_PER_USER))

echo "🏏 Cricket Commentary Service - Load Test"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Concurrent Users: $CONCURRENT_USERS"
echo "Requests per User: $REQUESTS_PER_USER"
echo "Total Requests: $TOTAL_REQUESTS"
echo ""

# Check if server is running
echo "Checking server health..."
if ! curl -f "$BASE_URL/actuator/health" > /dev/null 2>&1; then
    echo "❌ Server is not running at $BASE_URL"
    echo "Please start the server first: mvn spring-boot:run"
    exit 1
fi
echo "✅ Server is healthy"
echo ""

# Create a test match if needed
echo "Setting up test data..."
MATCH_ID=$(curl -s -X POST "$BASE_URL/api/matches" \
    -H "Content-Type: application/json" \
    -d '{
        "team1": "LoadTest Team A",
        "team2": "LoadTest Team B", 
        "venue": "Load Test Stadium",
        "matchDate": "'$(date -u +%Y-%m-%dT%H:%M:%S)'"
    }' | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$MATCH_ID" ]; then
    echo "❌ Failed to create test match"
    exit 1
fi

echo "✅ Created test match with ID: $MATCH_ID"

# Update match to live status
curl -s -X PUT "$BASE_URL/api/matches/$MATCH_ID/status?status=live" > /dev/null
echo "✅ Match set to live status"
echo ""

# Function to run load test for a specific endpoint
run_load_test() {
    local endpoint=$1
    local description=$2
    local method=${3:-GET}
    
    echo "Testing: $description"
    echo "Endpoint: $method $endpoint"
    
    if command -v ab > /dev/null 2>&1; then
        # Use Apache Bench if available
        if [ "$method" = "GET" ]; then
            ab -n $TOTAL_REQUESTS -c $CONCURRENT_USERS -q "$BASE_URL$endpoint" | grep -E "(Requests per second|Time per request|Transfer rate)"
        fi
    else
        # Fallback to curl-based test
        echo "Apache Bench not found, using curl for basic test..."
        start_time=$(date +%s)
        
        for i in $(seq 1 10); do
            curl -s "$BASE_URL$endpoint" > /dev/null &
        done
        wait
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "Completed 10 requests in ${duration}s"
    fi
    echo ""
}

# Test different endpoints
echo "🚀 Starting Load Tests..."
echo ""

# Test 1: Health check (baseline)
run_load_test "/actuator/health" "Health Check Endpoint"

# Test 2: Live matches (most critical endpoint)
run_load_test "/api/matches/live" "Live Matches Endpoint"

# Test 3: Match details
run_load_test "/api/matches/$MATCH_ID" "Match Details Endpoint"

# Test 4: Recent commentary (most frequently accessed)
run_load_test "/api/matches/$MATCH_ID/commentary/live" "Recent Commentary Endpoint"

# Test 5: All matches with pagination
run_load_test "/api/matches?page=0&size=20" "Paginated Matches Endpoint"

# Test 6: Match statistics
run_load_test "/api/matches/statistics" "Match Statistics Endpoint"

echo "🎯 Load Test Summary"
echo "==================="
echo "All endpoints tested with $CONCURRENT_USERS concurrent users"
echo "Total requests sent: $TOTAL_REQUESTS"
echo ""

# WebSocket Load Test (if wscat is available)
if command -v wscat > /dev/null 2>&1; then
    echo "🔌 Testing WebSocket Connections..."
    
    # Create multiple WebSocket connections
    for i in $(seq 1 10); do
        (
            echo "Connecting WebSocket client $i..."
            timeout 5s wscat -c "ws://localhost:8080/ws" > /dev/null 2>&1 || true
        ) &
    done
    wait
    
    echo "✅ WebSocket connection test completed"
else
    echo "⚠️  wscat not found, skipping WebSocket load test"
    echo "   Install with: npm install -g wscat"
fi

echo ""
echo "📊 Performance Recommendations:"
echo "- Response times should be < 100ms for cached endpoints"
echo "- Response times should be < 500ms for database queries"
echo "- Error rate should be < 1%"
echo "- WebSocket connections should establish quickly"
echo ""

# Cleanup
echo "🧹 Cleaning up test data..."
# Note: In a real scenario, you might want to clean up the test match
echo "Test match ID $MATCH_ID can be manually cleaned up if needed"

echo "✅ Load test completed!"
