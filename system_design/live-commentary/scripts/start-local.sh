#!/bin/bash

# Local Development Startup Script for Cricket Commentary Service
# This script sets up the local development environment

set -e

echo "🏏 Cricket Commentary Service - Local Setup"
echo "==========================================="

# Check if we're in the right directory
if [ ! -f "pom.xml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "Waiting for $service_name to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

echo ""
echo "📋 Checking prerequisites..."

# Check if Docker is available
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker is available"
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker not found - will skip containerized services"
    DOCKER_AVAILABLE=false
fi

# Check if Maven is available
if command -v mvn >/dev/null 2>&1; then
    echo "✅ Maven is available"
    MAVEN_AVAILABLE=true
else
    echo "⚠️  Maven not found - will try alternative build methods"
    MAVEN_AVAILABLE=false
fi

# Check if Java is available
if command -v java >/dev/null 2>&1; then
    echo "✅ Java is available"
    JAVA_AVAILABLE=true
    java -version
else
    echo "❌ Java not found - please install Java 17+"
    JAVA_AVAILABLE=false
fi

echo ""

# Start dependencies if Docker is available
if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "🐳 Starting dependencies with Docker..."
    
    # Check if MySQL is already running
    if check_port 3306; then
        echo "⚠️  Port 3306 is already in use (MySQL might be running)"
    else
        echo "Starting MySQL..."
        docker run -d \
            --name cricket-mysql \
            -e MYSQL_ROOT_PASSWORD=password \
            -e MYSQL_DATABASE=cricket_commentary \
            -e MYSQL_USER=cricket_user \
            -e MYSQL_PASSWORD=cricket_pass \
            -p 3306:3306 \
            -v "$(pwd)/src/main/resources/schema.sql:/docker-entrypoint-initdb.d/schema.sql" \
            mysql:8.0 \
            --default-authentication-plugin=mysql_native_password
    fi
    
    # Check if Redis is already running
    if check_port 6379; then
        echo "⚠️  Port 6379 is already in use (Redis might be running)"
    else
        echo "Starting Redis..."
        docker run -d \
            --name cricket-redis \
            -p 6379:6379 \
            redis:7-alpine \
            redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    fi
    
    # Wait for services to be ready
    echo ""
    echo "⏳ Waiting for dependencies to be ready..."
    sleep 10
    
    # Check MySQL
    if wait_for_service "mysql://localhost:3306" "MySQL"; then
        echo "✅ MySQL is ready"
    else
        echo "❌ MySQL failed to start"
    fi
    
    # Check Redis
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            echo "✅ Redis is ready"
        else
            echo "❌ Redis failed to start"
        fi
    else
        echo "⚠️  redis-cli not found, assuming Redis is ready"
    fi
    
else
    echo "⚠️  Docker not available - please ensure MySQL and Redis are running manually"
    echo "   MySQL: localhost:3306 (database: cricket_commentary)"
    echo "   Redis: localhost:6379"
fi

echo ""

# Build and run the application
if [ "$MAVEN_AVAILABLE" = true ]; then
    echo "🔨 Building application with Maven..."
    mvn clean compile -q
    
    if [ $? -eq 0 ]; then
        echo "✅ Build successful"
        
        echo ""
        echo "🚀 Starting Cricket Commentary Service..."
        echo "   Application will be available at: http://localhost:8080"
        echo "   Reader Interface: http://localhost:8080/reader.html"
        echo "   Commentator Interface: http://localhost:8080/commentator.html"
        echo "   API Documentation: http://localhost:8080/swagger-ui.html"
        echo ""
        echo "Press Ctrl+C to stop the application"
        echo ""
        
        # Run the application
        mvn spring-boot:run
        
    else
        echo "❌ Build failed"
        exit 1
    fi
    
elif [ "$JAVA_AVAILABLE" = true ]; then
    echo "⚠️  Maven not available, checking for pre-built JAR..."
    
    if [ -f "target/live-commentary-1.0.0.jar" ]; then
        echo "✅ Found pre-built JAR"
        echo "🚀 Starting Cricket Commentary Service..."
        java -jar target/live-commentary-1.0.0.jar
    else
        echo "❌ No pre-built JAR found and Maven not available"
        echo "   Please install Maven or build the application manually"
        exit 1
    fi
    
else
    echo "❌ Neither Maven nor Java available"
    echo "   Please install Java 17+ and Maven to run the application"
    exit 1
fi
