version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: cricket-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: cricket_commentary
      MYSQL_USER: cricket_user
      MYSQL_PASSWORD: cricket_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - cricket-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cricket-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - cricket-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # Application (for production deployment)
  app:
    build: .
    container_name: cricket-app
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: cricket_commentary
      DB_USERNAME: cricket_user
      DB_PASSWORD: cricket_pass
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cricket-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx Load Balancer (for scaling)
  nginx:
    image: nginx:alpine
    container_name: cricket-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    networks:
      - cricket-network
    profiles:
      - production

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: cricket-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - cricket-network
    profiles:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: cricket-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - cricket-network
    profiles:
      - monitoring

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cricket-network:
    driver: bridge
