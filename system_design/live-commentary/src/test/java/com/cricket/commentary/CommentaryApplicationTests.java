package com.cricket.commentary;

import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.transaction.annotation.Transactional;

import com.cricket.commentary.dto.CommentaryDto;
import com.cricket.commentary.dto.MatchDto;
import com.cricket.commentary.service.MatchService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Integration tests for Cricket Commentary Application
 * 
 * Tests the complete workflow from match creation to commentary addition
 * and real-time updates.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class CommentaryApplicationTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private MatchService matchService;



    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void contextLoads() {
        // Verify that the application context loads successfully
    }

    @Test
    void testCompleteCommentaryWorkflow() throws Exception {
        // 1. Create a new match
        MatchDto matchDto = new MatchDto();
        matchDto.setTeam1("India");
        matchDto.setTeam2("Australia");
        matchDto.setVenue("MCG");
        matchDto.setMatchDate(LocalDateTime.now().plusHours(1));

        String matchJson = objectMapper.writeValueAsString(matchDto);

        String matchResponse = mockMvc.perform(post("/api/matches")
                .contentType(MediaType.APPLICATION_JSON)
                .content(matchJson))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.team1").value("India"))
                .andExpect(jsonPath("$.team2").value("Australia"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        MatchDto createdMatch = objectMapper.readValue(matchResponse, MatchDto.class);
        Long matchId = createdMatch.getId();

        // 2. Update match status to live
        mockMvc.perform(put("/api/matches/" + matchId + "/status")
                .param("status", "live"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("live"));

        // 3. Add commentary for first ball
        CommentaryDto commentaryDto = new CommentaryDto();
        commentaryDto.setMatchId(matchId);
        commentaryDto.setOverNumber(1);
        commentaryDto.setBallNumber(1);
        commentaryDto.setCommentaryText("Kohli takes strike, facing Starc. Good length delivery outside off stump.");
        commentaryDto.setCommentatorId("john_doe");

        String commentaryJson = objectMapper.writeValueAsString(commentaryDto);

        mockMvc.perform(post("/api/commentary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(commentaryJson))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.overNumber").value(1))
                .andExpect(jsonPath("$.ballNumber").value(1))
                .andExpect(jsonPath("$.commentaryText").value("Kohli takes strike, facing Starc. Good length delivery outside off stump."));

        // 4. Add more commentary
        for (int ball = 2; ball <= 6; ball++) {
            CommentaryDto ballCommentary = new CommentaryDto();
            ballCommentary.setMatchId(matchId);
            ballCommentary.setOverNumber(1);
            ballCommentary.setBallNumber(ball);
            ballCommentary.setCommentaryText("Ball " + ball + " of over 1 - good cricket being played");
            ballCommentary.setCommentatorId("john_doe");

            String ballJson = objectMapper.writeValueAsString(ballCommentary);

            mockMvc.perform(post("/api/commentary")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(ballJson))
                    .andExpect(status().isCreated());
        }

        // 5. Get recent commentary
        mockMvc.perform(get("/api/matches/" + matchId + "/commentary/live"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(6));

        // 6. Get match with updated state
        mockMvc.perform(get("/api/matches/" + matchId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentOver").value(1))
                .andExpect(jsonPath("$.currentBall").value(6))
                .andExpect(jsonPath("$.totalBalls").value(6));

        // 7. Get live matches
        mockMvc.perform(get("/api/matches/live"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].id").value(matchId));

        // 8. Search commentary
        mockMvc.perform(get("/api/commentary/search")
                .param("q", "Kohli")
                .param("matchId", matchId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(1));
    }

    @Test
    void testMatchManagement() throws Exception {
        // Test getting matches by status
        mockMvc.perform(get("/api/matches")
                .param("status", "upcoming")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        // Test getting today's matches
        mockMvc.perform(get("/api/matches/today"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());

        // Test getting match statistics
        mockMvc.perform(get("/api/matches/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.upcomingMatches").isNumber())
                .andExpect(jsonPath("$.liveMatches").isNumber())
                .andExpect(jsonPath("$.completedMatches").isNumber());
    }

    @Test
    void testCommentaryValidation() throws Exception {
        // Create a match first
        MatchDto matchDto = new MatchDto();
        matchDto.setTeam1("England");
        matchDto.setTeam2("New Zealand");
        matchDto.setVenue("Lords");
        matchDto.setMatchDate(LocalDateTime.now().plusHours(2));

        MatchDto createdMatch = matchService.createMatch(matchDto);
        Long matchId = createdMatch.getId();

        // Test invalid ball number
        CommentaryDto invalidBall = new CommentaryDto();
        invalidBall.setMatchId(matchId);
        invalidBall.setOverNumber(1);
        invalidBall.setBallNumber(7); // Invalid - should be 1-6
        invalidBall.setCommentaryText("Invalid ball number");
        invalidBall.setCommentatorId("john_doe");

        String invalidJson = objectMapper.writeValueAsString(invalidBall);

        mockMvc.perform(post("/api/commentary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());

        // Test missing required fields
        CommentaryDto missingFields = new CommentaryDto();
        missingFields.setMatchId(matchId);
        // Missing over, ball, text, commentator

        String missingJson = objectMapper.writeValueAsString(missingFields);

        mockMvc.perform(post("/api/commentary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(missingJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testHealthEndpoints() throws Exception {
        // Test application health
        mockMvc.perform(get("/actuator/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"));

        // Test metrics endpoint
        mockMvc.perform(get("/actuator/metrics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.names").isArray());
    }

    @Test
    void testConcurrentCommentaryAddition() throws Exception {
        // Create a match
        MatchDto matchDto = new MatchDto();
        matchDto.setTeam1("Pakistan");
        matchDto.setTeam2("South Africa");
        matchDto.setVenue("Karachi");
        matchDto.setMatchDate(LocalDateTime.now().plusHours(3));

        MatchDto createdMatch = matchService.createMatch(matchDto);
        Long matchId = createdMatch.getId();

        // Try to add duplicate commentary (should fail)
        CommentaryDto commentary1 = new CommentaryDto();
        commentary1.setMatchId(matchId);
        commentary1.setOverNumber(1);
        commentary1.setBallNumber(1);
        commentary1.setCommentaryText("First commentary");
        commentary1.setCommentatorId("john_doe");

        CommentaryDto commentary2 = new CommentaryDto();
        commentary2.setMatchId(matchId);
        commentary2.setOverNumber(1);
        commentary2.setBallNumber(1); // Same ball
        commentary2.setCommentaryText("Duplicate commentary");
        commentary2.setCommentatorId("jane_smith");

        // Add first commentary
        String json1 = objectMapper.writeValueAsString(commentary1);
        mockMvc.perform(post("/api/commentary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json1))
                .andExpect(status().isCreated());

        // Try to add duplicate (should fail)
        String json2 = objectMapper.writeValueAsString(commentary2);
        mockMvc.perform(post("/api/commentary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json2))
                .andExpect(status().isBadRequest());
    }
}
