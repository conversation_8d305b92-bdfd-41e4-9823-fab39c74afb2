package com.cricket.commentary.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Data Transfer Object for Match entity
 * 
 * Used for API communication to avoid exposing internal entity structure
 * and provide validation for incoming requests.
 */
public class MatchDto {
    
    private Long id;
    
    @NotBlank(message = "Team1 name is required")
    @Size(max = 100, message = "Team1 name must not exceed 100 characters")
    private String team1;
    
    @NotBlank(message = "Team2 name is required")
    @Size(max = 100, message = "Team2 name must not exceed 100 characters")
    private String team2;
    
    @Size(max = 200, message = "Venue name must not exceed 200 characters")
    private String venue;
    
    @NotNull(message = "Match date is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime matchDate;
    
    private String status;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // Match state information (for live matches)
    private Integer currentOver;
    private Integer currentBall;
    private Integer totalBalls;
    private Double progressPercentage;
    
    // Constructors
    public MatchDto() {}
    
    public MatchDto(String team1, String team2, String venue, LocalDateTime matchDate) {
        this.team1 = team1;
        this.team2 = team2;
        this.venue = venue;
        this.matchDate = matchDate;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTeam1() {
        return team1;
    }
    
    public void setTeam1(String team1) {
        this.team1 = team1;
    }
    
    public String getTeam2() {
        return team2;
    }
    
    public void setTeam2(String team2) {
        this.team2 = team2;
    }
    
    public String getVenue() {
        return venue;
    }
    
    public void setVenue(String venue) {
        this.venue = venue;
    }
    
    public LocalDateTime getMatchDate() {
        return matchDate;
    }
    
    public void setMatchDate(LocalDateTime matchDate) {
        this.matchDate = matchDate;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Integer getCurrentOver() {
        return currentOver;
    }
    
    public void setCurrentOver(Integer currentOver) {
        this.currentOver = currentOver;
    }
    
    public Integer getCurrentBall() {
        return currentBall;
    }
    
    public void setCurrentBall(Integer currentBall) {
        this.currentBall = currentBall;
    }
    
    public Integer getTotalBalls() {
        return totalBalls;
    }
    
    public void setTotalBalls(Integer totalBalls) {
        this.totalBalls = totalBalls;
    }
    
    public Double getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(Double progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    // Utility methods
    public String getMatchTitle() {
        return team1 + " vs " + team2;
    }
    
    public boolean isLive() {
        return "live".equalsIgnoreCase(status);
    }
    
    public boolean isCompleted() {
        return "completed".equalsIgnoreCase(status);
    }
    
    public boolean isUpcoming() {
        return "upcoming".equalsIgnoreCase(status);
    }
    
    public String getCurrentBallIdentifier() {
        if (currentOver != null && currentBall != null) {
            return currentOver + "." + currentBall;
        }
        return "0.0";
    }
    
    @Override
    public String toString() {
        return "MatchDto{" +
                "id=" + id +
                ", team1='" + team1 + '\'' +
                ", team2='" + team2 + '\'' +
                ", venue='" + venue + '\'' +
                ", matchDate=" + matchDate +
                ", status='" + status + '\'' +
                ", currentOver=" + currentOver +
                ", currentBall=" + currentBall +
                ", totalBalls=" + totalBalls +
                '}';
    }
}
