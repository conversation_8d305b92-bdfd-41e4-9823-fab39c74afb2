package com.cricket.commentary.repository;

import com.cricket.commentary.model.Match;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Match entity
 * 
 * Provides optimized queries for high-read scenarios with proper indexing.
 * All queries are designed to leverage database indexes for performance.
 */
@Repository
public interface MatchRepository extends JpaRepository<Match, Long> {
    
    /**
     * Find matches by status with pagination
     * Uses idx_status index for efficient filtering
     */
    Page<Match> findByStatusOrderByMatchDateDesc(String status, Pageable pageable);
    
    /**
     * Find live matches
     * Optimized for frequent calls from the reader service
     */
    @Query("SELECT m FROM Match m WHERE m.status = 'LIVE' ORDER BY m.matchDate ASC")
    List<Match> findLiveMatches();
    
    /**
     * Find upcoming matches within date range
     * Uses idx_status_date composite index
     */
    @Query("SELECT m FROM Match m WHERE m.status = 'UPCOMING' " +
           "AND m.matchDate BETWEEN :startDate AND :endDate " +
           "ORDER BY m.matchDate ASC")
    List<Match> findUpcomingMatchesBetween(@Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find matches by teams (either team1 or team2)
     * Useful for team-specific match listings
     */
    @Query("SELECT m FROM Match m WHERE " +
           "(LOWER(m.team1) LIKE LOWER(CONCAT('%', :team, '%')) OR " +
           "LOWER(m.team2) LIKE LOWER(CONCAT('%', :team, '%'))) " +
           "ORDER BY m.matchDate DESC")
    List<Match> findMatchesByTeam(@Param("team") String team);
    
    /**
     * Find matches at a specific venue
     */
    @Query("SELECT m FROM Match m WHERE LOWER(m.venue) LIKE LOWER(CONCAT('%', :venue, '%')) " +
           "ORDER BY m.matchDate DESC")
    List<Match> findMatchesByVenue(@Param("venue") String venue);
    
    /**
     * Find recent completed matches
     * Uses idx_status_date index
     */
    @Query("SELECT m FROM Match m WHERE m.status = 'COMPLETED' " +
           "ORDER BY m.matchDate DESC")
    Page<Match> findRecentCompletedMatches(Pageable pageable);
    
    /**
     * Find matches scheduled for today
     * Optimized for dashboard queries
     */
    @Query("SELECT m FROM Match m WHERE " +
           "DATE(m.matchDate) = DATE(:date) " +
           "ORDER BY m.matchDate ASC")
    List<Match> findMatchesOnDate(@Param("date") LocalDateTime date);
    
    /**
     * Count matches by status
     * Used for statistics and monitoring
     */
    @Query("SELECT COUNT(m) FROM Match m WHERE m.status = :status")
    long countByStatus(@Param("status") String status);
    
    /**
     * Find match with detailed information including state
     * Custom query to avoid N+1 problem
     */
    @Query("SELECT m FROM Match m WHERE m.id = :matchId")
    Optional<Match> findByIdWithDetails(@Param("matchId") Long matchId);
    
    /**
     * Check if match exists and is live
     * Optimized for frequent validation calls
     */
    @Query("SELECT CASE WHEN COUNT(m) > 0 THEN true ELSE false END " +
           "FROM Match m WHERE m.id = :matchId AND m.status = 'LIVE'")
    boolean isMatchLive(@Param("matchId") Long matchId);
    
    /**
     * Find matches that should be marked as completed
     * Used by background jobs for status updates
     */
    @Query("SELECT m FROM Match m WHERE m.status = 'LIVE' " +
           "AND m.matchDate < :cutoffTime")
    List<Match> findMatchesToComplete(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * Find matches starting soon (for notifications)
     */
    @Query("SELECT m FROM Match m WHERE m.status = 'UPCOMING' " +
           "AND m.matchDate BETWEEN :now AND :futureTime " +
           "ORDER BY m.matchDate ASC")
    List<Match> findMatchesStartingSoon(@Param("now") LocalDateTime now,
                                       @Param("futureTime") LocalDateTime futureTime);
    
    /**
     * Search matches by multiple criteria
     * Full-text search capability
     */
    @Query("SELECT m FROM Match m WHERE " +
           "(LOWER(m.team1) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(m.team2) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(m.venue) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND (:status IS NULL OR m.status = :status) " +
           "ORDER BY m.matchDate DESC")
    Page<Match> searchMatches(@Param("searchTerm") String searchTerm,
                             @Param("status") String status,
                             Pageable pageable);
    
    /**
     * Get match statistics for monitoring
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN m.status = 'UPCOMING' THEN 1 END) as upcoming, " +
           "COUNT(CASE WHEN m.status = 'LIVE' THEN 1 END) as live, " +
           "COUNT(CASE WHEN m.status = 'COMPLETED' THEN 1 END) as completed " +
           "FROM Match m")
    Object[] getMatchStatistics();
}
