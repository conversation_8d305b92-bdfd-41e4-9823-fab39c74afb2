package com.cricket.commentary.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Entity representing ball-by-ball cricket commentary
 * 
 * This entity is optimized for high-frequency reads and sequential writes.
 * The unique constraint ensures no duplicate commentary for the same ball.
 */
@Entity
@Table(name = "commentary", 
    indexes = {
        @Index(name = "idx_match_over_ball", columnList = "matchId, overNumber, ballNumber"),
        @Index(name = "idx_match_timestamp", columnList = "matchId, timestamp"),
        @Index(name = "idx_timestamp", columnList = "timestamp"),
        @Index(name = "idx_commentator", columnList = "commentatorId")
    },
    uniqueConstraints = {
        @UniqueConstraint(name = "uk_match_over_ball", 
                         columnNames = {"matchId", "overNumber", "ballNumber"})
    }
)
public class Commentary {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "Match ID is required")
    @Column(name = "match_id", nullable = false)
    private Long matchId;
    
    @NotNull(message = "Over number is required")
    @Min(value = 1, message = "Over number must be at least 1")
    @Column(name = "over_number", nullable = false)
    private Integer overNumber;
    
    @NotNull(message = "Ball number is required")
    @Min(value = 1, message = "Ball number must be between 1 and 6")
    @Column(name = "ball_number", nullable = false)
    private Integer ballNumber;
    
    @NotBlank(message = "Commentary text is required")
    @Size(max = 1000, message = "Commentary text must not exceed 1000 characters")
    @Column(name = "commentary_text", nullable = false, columnDefinition = "TEXT")
    private String commentaryText;
    
    @NotBlank(message = "Commentator ID is required")
    @Size(max = 50, message = "Commentator ID must not exceed 50 characters")
    @Column(name = "commentator_id", nullable = false, length = 50)
    private String commentatorId;
    
    @Column(name = "timestamp", nullable = false, updatable = false)
    private LocalDateTime timestamp;
    
    // Constructors
    public Commentary() {}
    
    public Commentary(Long matchId, Integer overNumber, Integer ballNumber, 
                     String commentaryText, String commentatorId) {
        this.matchId = matchId;
        this.overNumber = overNumber;
        this.ballNumber = ballNumber;
        this.commentaryText = commentaryText;
        this.commentatorId = commentatorId;
    }
    
    // Lifecycle callbacks
    @PrePersist
    protected void onCreate() {
        if (timestamp == null) {
            timestamp = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMatchId() {
        return matchId;
    }
    
    public void setMatchId(Long matchId) {
        this.matchId = matchId;
    }
    
    public Integer getOverNumber() {
        return overNumber;
    }
    
    public void setOverNumber(Integer overNumber) {
        this.overNumber = overNumber;
    }
    
    public Integer getBallNumber() {
        return ballNumber;
    }
    
    public void setBallNumber(Integer ballNumber) {
        this.ballNumber = ballNumber;
    }
    
    public String getCommentaryText() {
        return commentaryText;
    }
    
    public void setCommentaryText(String commentaryText) {
        this.commentaryText = commentaryText;
    }
    
    public String getCommentatorId() {
        return commentatorId;
    }
    
    public void setCommentatorId(String commentatorId) {
        this.commentatorId = commentatorId;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    // Business methods
    public String getBallIdentifier() {
        return overNumber + "." + ballNumber;
    }
    
    public boolean isValidBall() {
        return ballNumber >= 1 && ballNumber <= 6;
    }
    
    public int getTotalBalls() {
        return (overNumber - 1) * 6 + ballNumber;
    }
    
    // toString, equals, hashCode
    @Override
    public String toString() {
        return "Commentary{" +
                "id=" + id +
                ", matchId=" + matchId +
                ", overNumber=" + overNumber +
                ", ballNumber=" + ballNumber +
                ", commentaryText='" + commentaryText + '\'' +
                ", commentatorId='" + commentatorId + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Commentary)) return false;
        Commentary that = (Commentary) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
