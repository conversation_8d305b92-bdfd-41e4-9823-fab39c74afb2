package com.cricket.commentary.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket configuration for real-time communication
 * 
 * Configures STOMP messaging for broadcasting commentary updates
 * to 5M+ concurrent users with optimal performance.
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Enable simple broker for topics
        config.enableSimpleBroker("/topic", "/queue")
              .setHeartbeatValue(new long[]{30000, 30000}) // 30 second heartbeat
              .setTaskScheduler(null); // Use default scheduler
        
        // Set application destination prefix
        config.setApplicationDestinationPrefixes("/app");
        
        // Set user destination prefix
        config.setUserDestinationPrefix("/user");
    }
    
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Register WebSocket endpoint with SockJS fallback
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*") // Configure properly for production
                .withSockJS()
                .setHeartbeatTime(30000) // 30 second heartbeat
                .setDisconnectDelay(5000) // 5 second disconnect delay
                .setStreamBytesLimit(128 * 1024) // 128KB stream limit
                .setHttpMessageCacheSize(1000); // Cache 1000 messages
        
        // Register native WebSocket endpoint (for better performance)
        registry.addEndpoint("/ws-native")
                .setAllowedOriginPatterns("*");
    }
}
