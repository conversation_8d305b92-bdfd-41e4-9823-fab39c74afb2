package com.cricket.commentary.config;

import java.util.concurrent.TimeUnit;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.github.benmanes.caffeine.cache.Caffeine;

/**
 * Cache configuration for multi-level caching strategy
 * 
 * Implements L1 (Caffeine) and L2 (Redis) caching for optimal performance.
 * Designed to handle 5M+ concurrent users with minimal latency.
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * Configure Caffeine cache manager for L1 caching
     * Fast in-memory cache with automatic eviction
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Configure default cache settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(10000) // Maximum 10K entries per cache
                .expireAfterWrite(60, TimeUnit.SECONDS) // 1 minute TTL
                .expireAfterAccess(30, TimeUnit.SECONDS) // 30 seconds idle timeout
                .recordStats()); // Enable statistics
        
        // Define cache names
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "liveMatches",
                "matchDetails",
                "matchesByStatus",
                "recentCommentary",
                "matchCommentary",
                "overCommentary",
                "ballCommentary",
                "commentaryStats",
                "matchStats",
                "matchStatus",
                "teamMatches",
                "recentMatches",
                "todayMatches",
                "matchSearch"
        ));
        
        return cacheManager;
    }
    
    /**
     * Configure specific cache for live matches (most frequently accessed)
     */
    @Bean
    public Caffeine<Object, Object> liveMatchesCaffeine() {
        return Caffeine.newBuilder()
                .maximumSize(1000) // Smaller cache for live matches
                .expireAfterWrite(30, TimeUnit.SECONDS) // Shorter TTL for live data
                .expireAfterAccess(15, TimeUnit.SECONDS)
                .recordStats();
    }
    
    /**
     * Configure cache for recent commentary (critical for performance)
     */
    @Bean
    public Caffeine<Object, Object> recentCommentaryCaffeine() {
        return Caffeine.newBuilder()
                .maximumSize(5000) // Larger cache for commentary
                .expireAfterWrite(60, TimeUnit.SECONDS)
                .expireAfterAccess(30, TimeUnit.SECONDS)
                .recordStats();
    }
    
    /**
     * Configure cache for match details
     */
    @Bean
    public Caffeine<Object, Object> matchDetailsCaffeine() {
        return Caffeine.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(300, TimeUnit.SECONDS) // 5 minutes for match details
                .expireAfterAccess(120, TimeUnit.SECONDS)
                .recordStats();
    }
}
