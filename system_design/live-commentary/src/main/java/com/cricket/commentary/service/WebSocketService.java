package com.cricket.commentary.service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import com.cricket.commentary.dto.CommentaryDto;

/**
 * Service for WebSocket-based real-time communication
 * 
 * <PERSON>les broadcasting commentary updates to connected clients.
 * Optimized for high-concurrency scenarios with 5M+ connections.
 */
@Service
public class WebSocketService {
    
    private final SimpMessagingTemplate messagingTemplate;
    
    // Connection tracking
    private final ConcurrentHashMap<String, Long> sessionToMatch = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, AtomicLong> matchConnections = new ConcurrentHashMap<>();
    
    @Autowired
    public WebSocketService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * Broadcast commentary update to all subscribers of a match
     */
    public void broadcastCommentary(CommentaryDto commentary) {
        try {
            String destination = "/topic/match/" + commentary.getMatchId() + "/commentary";
            messagingTemplate.convertAndSend(destination, commentary);
            
            // Also send to general live updates channel
            messagingTemplate.convertAndSend("/topic/live-updates", commentary);
            
        } catch (Exception e) {
            // Log error but don't fail the commentary creation
            System.err.println("Failed to broadcast commentary: " + e.getMessage());
        }
    }
    
    /**
     * Broadcast match state update
     */
    public void broadcastMatchState(Long matchId, Object matchState) {
        try {
            String destination = "/topic/match/" + matchId + "/state";
            messagingTemplate.convertAndSend(destination, matchState);
        } catch (Exception e) {
            System.err.println("Failed to broadcast match state: " + e.getMessage());
        }
    }
    
    /**
     * Register a session for a specific match
     */
    public void registerSession(String sessionId, Long matchId) {
        sessionToMatch.put(sessionId, matchId);
        matchConnections.computeIfAbsent(matchId, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * Unregister a session
     */
    public void unregisterSession(String sessionId) {
        Long matchId = sessionToMatch.remove(sessionId);
        if (matchId != null) {
            AtomicLong count = matchConnections.get(matchId);
            if (count != null) {
                long newCount = count.decrementAndGet();
                if (newCount <= 0) {
                    matchConnections.remove(matchId);
                }
            }
        }
    }
    
    /**
     * Get connection count for a match
     */
    public long getConnectionCount(Long matchId) {
        AtomicLong count = matchConnections.get(matchId);
        return count != null ? count.get() : 0;
    }
    
    /**
     * Get total active connections
     */
    public long getTotalConnections() {
        return sessionToMatch.size();
    }
    
    /**
     * Send message to specific session
     */
    public void sendToSession(String sessionId, String destination, Object message) {
        try {
            messagingTemplate.convertAndSendToUser(sessionId, destination, message);
        } catch (Exception e) {
            System.err.println("Failed to send message to session " + sessionId + ": " + e.getMessage());
        }
    }
    
    /**
     * Broadcast system message to all connected clients
     */
    public void broadcastSystemMessage(String message) {
        try {
            messagingTemplate.convertAndSend("/topic/system", message);
        } catch (Exception e) {
            System.err.println("Failed to broadcast system message: " + e.getMessage());
        }
    }
}
