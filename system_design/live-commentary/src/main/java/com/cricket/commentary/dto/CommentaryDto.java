package com.cricket.commentary.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

/**
 * Data Transfer Object for Commentary entity
 * 
 * Used for API communication with validation rules for commentary creation.
 * Includes additional fields for enhanced user experience.
 */
public class CommentaryDto {
    
    private Long id;
    
    @NotNull(message = "Match ID is required")
    private Long matchId;
    
    @NotNull(message = "Over number is required")
    @Min(value = 1, message = "Over number must be at least 1")
    @Max(value = 50, message = "Over number must not exceed 50")
    private Integer overNumber;
    
    @NotNull(message = "Ball number is required")
    @Min(value = 1, message = "Ball number must be between 1 and 6")
    @Max(value = 6, message = "Ball number must be between 1 and 6")
    private Integer ballNumber;
    
    @NotBlank(message = "Commentary text is required")
    @Size(min = 10, max = 1000, message = "Commentary text must be between 10 and 1000 characters")
    private String commentaryText;
    
    @NotBlank(message = "Commentator ID is required")
    @Size(max = 50, message = "Commentator ID must not exceed 50 characters")
    private String commentatorId;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;
    
    // Additional fields for enhanced response
    private String commentatorName;
    private String ballIdentifier;
    private Integer totalBalls;
    private String matchTitle;
    
    // Constructors
    public CommentaryDto() {}
    
    public CommentaryDto(Long matchId, Integer overNumber, Integer ballNumber, 
                        String commentaryText, String commentatorId) {
        this.matchId = matchId;
        this.overNumber = overNumber;
        this.ballNumber = ballNumber;
        this.commentaryText = commentaryText;
        this.commentatorId = commentatorId;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMatchId() {
        return matchId;
    }
    
    public void setMatchId(Long matchId) {
        this.matchId = matchId;
    }
    
    public Integer getOverNumber() {
        return overNumber;
    }
    
    public void setOverNumber(Integer overNumber) {
        this.overNumber = overNumber;
        updateBallIdentifier();
        updateTotalBalls();
    }
    
    public Integer getBallNumber() {
        return ballNumber;
    }
    
    public void setBallNumber(Integer ballNumber) {
        this.ballNumber = ballNumber;
        updateBallIdentifier();
        updateTotalBalls();
    }
    
    public String getCommentaryText() {
        return commentaryText;
    }
    
    public void setCommentaryText(String commentaryText) {
        this.commentaryText = commentaryText;
    }
    
    public String getCommentatorId() {
        return commentatorId;
    }
    
    public void setCommentatorId(String commentatorId) {
        this.commentatorId = commentatorId;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getCommentatorName() {
        return commentatorName;
    }
    
    public void setCommentatorName(String commentatorName) {
        this.commentatorName = commentatorName;
    }
    
    public String getBallIdentifier() {
        return ballIdentifier;
    }
    
    public void setBallIdentifier(String ballIdentifier) {
        this.ballIdentifier = ballIdentifier;
    }
    
    public Integer getTotalBalls() {
        return totalBalls;
    }
    
    public void setTotalBalls(Integer totalBalls) {
        this.totalBalls = totalBalls;
    }
    
    public String getMatchTitle() {
        return matchTitle;
    }
    
    public void setMatchTitle(String matchTitle) {
        this.matchTitle = matchTitle;
    }
    
    // Utility methods
    private void updateBallIdentifier() {
        if (overNumber != null && ballNumber != null) {
            this.ballIdentifier = overNumber + "." + ballNumber;
        }
    }
    
    private void updateTotalBalls() {
        if (overNumber != null && ballNumber != null) {
            this.totalBalls = (overNumber - 1) * 6 + ballNumber;
        }
    }
    
    public boolean isValidBall() {
        return ballNumber != null && ballNumber >= 1 && ballNumber <= 6;
    }
    
    public boolean isOverComplete() {
        return ballNumber != null && ballNumber == 6;
    }
    
    @Override
    public String toString() {
        return "CommentaryDto{" +
                "id=" + id +
                ", matchId=" + matchId +
                ", overNumber=" + overNumber +
                ", ballNumber=" + ballNumber +
                ", commentaryText='" + commentaryText + '\'' +
                ", commentatorId='" + commentatorId + '\'' +
                ", timestamp=" + timestamp +
                ", ballIdentifier='" + ballIdentifier + '\'' +
                ", totalBalls=" + totalBalls +
                '}';
    }
}
