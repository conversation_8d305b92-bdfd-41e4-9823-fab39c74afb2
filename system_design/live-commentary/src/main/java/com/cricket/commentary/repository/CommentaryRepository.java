package com.cricket.commentary.repository;

import com.cricket.commentary.model.Commentary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Commentary entity
 * 
 * Optimized for high-frequency reads with minimal writes.
 * All queries leverage composite indexes for maximum performance.
 */
@Repository
public interface CommentaryRepository extends JpaRepository<Commentary, Long> {
    
    /**
     * Find commentary for a specific match ordered by ball sequence
     * Uses idx_match_over_ball composite index for optimal performance
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "ORDER BY c.overNumber ASC, c.ballNumber ASC")
    List<Commentary> findByMatchIdOrderBySequence(@Param("matchId") Long matchId);
    
    /**
     * Find recent commentary for a match (last N balls)
     * Critical for live updates - heavily cached
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> findRecentCommentaryByMatchId(@Param("matchId") Long matchId, 
                                                   Pageable pageable);
    
    /**
     * Find commentary for a specific over
     * Uses idx_match_over_ball index
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "AND c.overNumber = :overNumber " +
           "ORDER BY c.ballNumber ASC")
    List<Commentary> findByMatchIdAndOverNumber(@Param("matchId") Long matchId,
                                               @Param("overNumber") Integer overNumber);
    
    /**
     * Find commentary for a specific ball
     * Uses unique constraint uk_match_over_ball
     */
    Optional<Commentary> findByMatchIdAndOverNumberAndBallNumber(
            @Param("matchId") Long matchId,
            @Param("overNumber") Integer overNumber,
            @Param("ballNumber") Integer ballNumber);
    
    /**
     * Find latest commentary for a match
     * Used to determine current match state
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "ORDER BY c.timestamp DESC")
    Optional<Commentary> findLatestCommentaryByMatchId(@Param("matchId") Long matchId);
    
    /**
     * Find commentary within a time range
     * Uses idx_match_timestamp index
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "AND c.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY c.timestamp ASC")
    List<Commentary> findByMatchIdAndTimestampBetween(
            @Param("matchId") Long matchId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * Find commentary by commentator
     * Uses idx_commentator index
     */
    @Query("SELECT c FROM Commentary c WHERE c.commentatorId = :commentatorId " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> findByCommentatorId(@Param("commentatorId") String commentatorId,
                                        Pageable pageable);
    
    /**
     * Count total commentary entries for a match
     * Used for statistics and progress tracking
     */
    @Query("SELECT COUNT(c) FROM Commentary c WHERE c.matchId = :matchId")
    long countByMatchId(@Param("matchId") Long matchId);
    
    /**
     * Find commentary after a specific timestamp
     * Used for incremental updates and WebSocket synchronization
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId = :matchId " +
           "AND c.timestamp > :afterTimestamp " +
           "ORDER BY c.timestamp ASC")
    List<Commentary> findByMatchIdAfterTimestamp(@Param("matchId") Long matchId,
                                                @Param("afterTimestamp") LocalDateTime afterTimestamp);
    
    /**
     * Find commentary for multiple matches (for dashboard)
     * Optimized for admin interfaces
     */
    @Query("SELECT c FROM Commentary c WHERE c.matchId IN :matchIds " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> findByMatchIdIn(@Param("matchIds") List<Long> matchIds,
                                    Pageable pageable);
    
    /**
     * Get commentary statistics for a match
     * Used for match summary and analytics
     */
    @Query("SELECT " +
           "COUNT(c) as totalBalls, " +
           "MAX(c.overNumber) as maxOver, " +
           "MAX(c.ballNumber) as maxBall, " +
           "MIN(c.timestamp) as firstBall, " +
           "MAX(c.timestamp) as lastBall " +
           "FROM Commentary c WHERE c.matchId = :matchId")
    Object[] getCommentaryStatistics(@Param("matchId") Long matchId);
    
    /**
     * Find commentary with pagination and filtering
     * General purpose query for admin interfaces
     */
    @Query("SELECT c FROM Commentary c WHERE " +
           "(:matchId IS NULL OR c.matchId = :matchId) " +
           "AND (:commentatorId IS NULL OR c.commentatorId = :commentatorId) " +
           "AND (:fromDate IS NULL OR c.timestamp >= :fromDate) " +
           "AND (:toDate IS NULL OR c.timestamp <= :toDate) " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> findCommentaryWithFilters(
            @Param("matchId") Long matchId,
            @Param("commentatorId") String commentatorId,
            @Param("fromDate") LocalDateTime fromDate,
            @Param("toDate") LocalDateTime toDate,
            Pageable pageable);
    
    /**
     * Check if commentary exists for a specific ball
     * Used for validation before creating new commentary
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END " +
           "FROM Commentary c WHERE c.matchId = :matchId " +
           "AND c.overNumber = :overNumber AND c.ballNumber = :ballNumber")
    boolean existsByMatchIdAndOverNumberAndBallNumber(
            @Param("matchId") Long matchId,
            @Param("overNumber") Integer overNumber,
            @Param("ballNumber") Integer ballNumber);
    
    /**
     * Find commentary for live matches only
     * Optimized for real-time updates
     */
    @Query("SELECT c FROM Commentary c " +
           "JOIN Match m ON c.matchId = m.id " +
           "WHERE m.status = 'LIVE' " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> findLiveMatchCommentary(Pageable pageable);
    
    /**
     * Delete old commentary (for data retention)
     * Used by cleanup jobs
     */
    @Query("DELETE FROM Commentary c WHERE c.timestamp < :cutoffDate")
    void deleteCommentaryOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Find commentary by text search
     * Full-text search capability
     */
    @Query("SELECT c FROM Commentary c WHERE " +
           "LOWER(c.commentaryText) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "AND (:matchId IS NULL OR c.matchId = :matchId) " +
           "ORDER BY c.timestamp DESC")
    Page<Commentary> searchCommentaryByText(@Param("searchTerm") String searchTerm,
                                           @Param("matchId") Long matchId,
                                           Pageable pageable);
}
