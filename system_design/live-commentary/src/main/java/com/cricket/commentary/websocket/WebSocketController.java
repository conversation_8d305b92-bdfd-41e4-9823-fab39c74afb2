package com.cricket.commentary.websocket;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;
import org.springframework.web.socket.messaging.SessionUnsubscribeEvent;

import com.cricket.commentary.service.WebSocketService;

/**
 * WebSocket Controller for real-time communication
 * 
 * Handles WebSocket connections and subscriptions for live commentary updates.
 * Optimized for 5M+ concurrent connections with efficient message broadcasting.
 */
@Controller
public class WebSocketController {
    
    private final WebSocketService webSocketService;
    
    @Autowired
    public WebSocketController(WebSocketService webSocketService) {
        this.webSocketService = webSocketService;
    }
    
    /**
     * Handle subscription to match commentary
     */
    @SubscribeMapping("/topic/match/{matchId}/commentary")
    public void subscribeToMatchCommentary(@DestinationVariable Long matchId, 
                                         SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        webSocketService.registerSession(sessionId, matchId);
        
        // Send current connection count
        long connectionCount = webSocketService.getConnectionCount(matchId);
        webSocketService.sendToSession(sessionId, "/queue/connection-count", connectionCount);
    }
    
    /**
     * Handle subscription to match state updates
     */
    @SubscribeMapping("/topic/match/{matchId}/state")
    public void subscribeToMatchState(@DestinationVariable Long matchId, 
                                    SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        webSocketService.registerSession(sessionId, matchId);
    }
    
    /**
     * Handle subscription to live updates (all matches)
     */
    @SubscribeMapping("/topic/live-updates")
    public void subscribeToLiveUpdates(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        // Register for general live updates (no specific match)
        webSocketService.registerSession(sessionId, 0L);
    }
    
    /**
     * Handle ping messages for connection health check
     */
    @MessageMapping("/ping")
    @SendTo("/topic/pong")
    public String handlePing(String message) {
        return "pong";
    }
    
    /**
     * Handle client heartbeat
     */
    @MessageMapping("/heartbeat")
    public void handleHeartbeat(SimpMessageHeaderAccessor headerAccessor) {
        // Update last activity timestamp for the session
        // Could implement session activity tracking here
        // String sessionId = headerAccessor.getSessionId();
    }
    
    /**
     * Handle connection established event
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectEvent event) {
        SimpMessageHeaderAccessor headers = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String sessionId = headers.getSessionId();
        
        // Log connection (in production, use proper logging)
        System.out.println("WebSocket connection established: " + sessionId);
        
        // Send welcome message
        webSocketService.sendToSession(sessionId, "/queue/welcome", 
                "Connected to Cricket Commentary Service");
    }
    
    /**
     * Handle connection closed event
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        SimpMessageHeaderAccessor headers = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String sessionId = headers.getSessionId();
        
        // Unregister session
        webSocketService.unregisterSession(sessionId);
        
        // Log disconnection
        System.out.println("WebSocket connection closed: " + sessionId);
    }
    
    /**
     * Handle subscription event
     */
    @EventListener
    public void handleSubscriptionEvent(SessionSubscribeEvent event) {
        SimpMessageHeaderAccessor headers = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String sessionId = headers.getSessionId();
        String destination = headers.getDestination();
        
        // Log subscription
        System.out.println("Session " + sessionId + " subscribed to " + destination);
        
        // Send subscription confirmation
        webSocketService.sendToSession(sessionId, "/queue/subscription-confirmed", 
                Map.of("destination", destination, "status", "subscribed"));
    }
    
    /**
     * Handle unsubscription event
     */
    @EventListener
    public void handleUnsubscriptionEvent(SessionUnsubscribeEvent event) {
        SimpMessageHeaderAccessor headers = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String sessionId = headers.getSessionId();
        
        // Could implement specific unsubscription logic here
        System.out.println("Session " + sessionId + " unsubscribed");
    }
}
