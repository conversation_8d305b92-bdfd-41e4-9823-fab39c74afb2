package com.cricket.commentary.controller;

import com.cricket.commentary.dto.MatchDto;
import com.cricket.commentary.service.MatchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Match operations
 * 
 * Provides endpoints for match management optimized for high-read scenarios.
 * Supports 5M+ concurrent users with efficient caching and pagination.
 */
@RestController
@RequestMapping("/api/matches")
@Tag(name = "Match Management", description = "APIs for managing cricket matches")
@CrossOrigin(origins = "*") // Configure properly for production
public class MatchController {
    
    private final MatchService matchService;
    
    @Autowired
    public MatchController(MatchService matchService) {
        this.matchService = matchService;
    }
    
    /**
     * Get all live matches
     * Most frequently called endpoint - heavily cached
     */
    @GetMapping("/live")
    @Operation(summary = "Get all live matches", 
               description = "Returns all currently live matches with real-time state")
    public ResponseEntity<List<MatchDto>> getLiveMatches() {
        List<MatchDto> liveMatches = matchService.getLiveMatches();
        return ResponseEntity.ok(liveMatches);
    }
    
    /**
     * Get match by ID
     */
    @GetMapping("/{matchId}")
    @Operation(summary = "Get match by ID", 
               description = "Returns detailed match information including current state")
    public ResponseEntity<MatchDto> getMatchById(
            @Parameter(description = "Match ID") @PathVariable Long matchId) {
        Optional<MatchDto> match = matchService.getMatchById(matchId);
        return match.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get matches by status with pagination
     */
    @GetMapping
    @Operation(summary = "Get matches by status", 
               description = "Returns paginated list of matches filtered by status")
    public ResponseEntity<Page<MatchDto>> getMatchesByStatus(
            @Parameter(description = "Match status (upcoming, live, completed)") 
            @RequestParam(defaultValue = "upcoming") String status,
            @Parameter(description = "Page number (0-based)") 
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") 
            @RequestParam(defaultValue = "20") int size) {
        
        Page<MatchDto> matches = matchService.getMatchesByStatus(status, page, size);
        return ResponseEntity.ok(matches);
    }
    
    /**
     * Search matches
     */
    @GetMapping("/search")
    @Operation(summary = "Search matches", 
               description = "Search matches by team names or venue")
    public ResponseEntity<Page<MatchDto>> searchMatches(
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Filter by status") @RequestParam(required = false) String status,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<MatchDto> matches = matchService.searchMatches(q, status, page, size);
        return ResponseEntity.ok(matches);
    }
    
    /**
     * Get today's matches
     */
    @GetMapping("/today")
    @Operation(summary = "Get today's matches", 
               description = "Returns all matches scheduled for today")
    public ResponseEntity<List<MatchDto>> getTodayMatches() {
        List<MatchDto> matches = matchService.getTodayMatches();
        return ResponseEntity.ok(matches);
    }
    
    /**
     * Get matches by team
     */
    @GetMapping("/team/{teamName}")
    @Operation(summary = "Get matches by team", 
               description = "Returns all matches for a specific team")
    public ResponseEntity<List<MatchDto>> getMatchesByTeam(
            @Parameter(description = "Team name") @PathVariable String teamName) {
        List<MatchDto> matches = matchService.getMatchesByTeam(teamName);
        return ResponseEntity.ok(matches);
    }
    
    /**
     * Get recent completed matches
     */
    @GetMapping("/recent")
    @Operation(summary = "Get recent completed matches", 
               description = "Returns recently completed matches with pagination")
    public ResponseEntity<Page<MatchDto>> getRecentMatches(
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size) {
        
        Page<MatchDto> matches = matchService.getRecentCompletedMatches(page, size);
        return ResponseEntity.ok(matches);
    }
    
    /**
     * Create new match (Commentator/Admin only)
     */
    @PostMapping
    @Operation(summary = "Create new match", 
               description = "Creates a new cricket match")
    public ResponseEntity<MatchDto> createMatch(@Valid @RequestBody MatchDto matchDto) {
        try {
            MatchDto createdMatch = matchService.createMatch(matchDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdMatch);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Update match status (Admin only)
     */
    @PutMapping("/{matchId}/status")
    @Operation(summary = "Update match status", 
               description = "Updates the status of a match (upcoming/live/completed)")
    public ResponseEntity<MatchDto> updateMatchStatus(
            @Parameter(description = "Match ID") @PathVariable Long matchId,
            @Parameter(description = "New status") @RequestParam String status) {
        try {
            MatchDto updatedMatch = matchService.updateMatchStatus(matchId, status);
            return ResponseEntity.ok(updatedMatch);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Check if match is live
     */
    @GetMapping("/{matchId}/live")
    @Operation(summary = "Check if match is live", 
               description = "Returns true if the match is currently live")
    public ResponseEntity<Boolean> isMatchLive(
            @Parameter(description = "Match ID") @PathVariable Long matchId) {
        boolean isLive = matchService.isMatchLive(matchId);
        return ResponseEntity.ok(isLive);
    }
    
    /**
     * Get match statistics
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get match statistics", 
               description = "Returns overall match statistics")
    public ResponseEntity<MatchService.MatchStatistics> getMatchStatistics() {
        MatchService.MatchStatistics stats = matchService.getMatchStatistics();
        return ResponseEntity.ok(stats);
    }
}
