package com.cricket.commentary.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Entity representing a cricket match
 * 
 * This entity stores basic match information and is optimized for
 * high-read scenarios with appropriate indexing.
 */
@Entity
@Table(name = "matches", indexes = {
    @Index(name = "idx_status_date", columnList = "status, matchDate"),
    @Index(name = "idx_match_date", columnList = "matchDate"),
    @Index(name = "idx_status", columnList = "status")
})
public class Match {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Team1 name is required")
    @Size(max = 100, message = "Team1 name must not exceed 100 characters")
    @Column(name = "team1", nullable = false, length = 100)
    private String team1;
    
    @NotBlank(message = "Team2 name is required")
    @Size(max = 100, message = "Team2 name must not exceed 100 characters")
    @Column(name = "team2", nullable = false, length = 100)
    private String team2;
    
    @Size(max = 200, message = "Venue name must not exceed 200 characters")
    @Column(name = "venue", length = 200)
    private String venue;
    
    @NotNull(message = "Match date is required")
    @Column(name = "match_date", nullable = false)
    private LocalDateTime matchDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private MatchStatus status = MatchStatus.UPCOMING;
    
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Match() {}
    
    public Match(String team1, String team2, String venue, LocalDateTime matchDate) {
        this.team1 = team1;
        this.team2 = team2;
        this.venue = venue;
        this.matchDate = matchDate;
        this.status = MatchStatus.UPCOMING;
    }
    
    // Lifecycle callbacks
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTeam1() {
        return team1;
    }
    
    public void setTeam1(String team1) {
        this.team1 = team1;
    }
    
    public String getTeam2() {
        return team2;
    }
    
    public void setTeam2(String team2) {
        this.team2 = team2;
    }
    
    public String getVenue() {
        return venue;
    }
    
    public void setVenue(String venue) {
        this.venue = venue;
    }
    
    public LocalDateTime getMatchDate() {
        return matchDate;
    }
    
    public void setMatchDate(LocalDateTime matchDate) {
        this.matchDate = matchDate;
    }
    
    public MatchStatus getStatus() {
        return status;
    }
    
    public void setStatus(MatchStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Business methods
    public boolean isLive() {
        return status == MatchStatus.LIVE;
    }
    
    public boolean isCompleted() {
        return status == MatchStatus.COMPLETED;
    }
    
    public boolean isUpcoming() {
        return status == MatchStatus.UPCOMING;
    }
    
    public String getMatchTitle() {
        return team1 + " vs " + team2;
    }
    
    // toString, equals, hashCode
    @Override
    public String toString() {
        return "Match{" +
                "id=" + id +
                ", team1='" + team1 + '\'' +
                ", team2='" + team2 + '\'' +
                ", venue='" + venue + '\'' +
                ", matchDate=" + matchDate +
                ", status=" + status +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Match)) return false;
        Match match = (Match) o;
        return id != null && id.equals(match.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}


