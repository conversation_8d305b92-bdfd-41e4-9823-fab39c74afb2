package com.cricket.commentary.model;

/**
 * Enum representing the status of a cricket match
 */
public enum MatchStatus {
    UPCOMING("upcoming"),
    LIVE("live"),
    COMPLETED("completed");
    
    private final String value;
    
    MatchStatus(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
