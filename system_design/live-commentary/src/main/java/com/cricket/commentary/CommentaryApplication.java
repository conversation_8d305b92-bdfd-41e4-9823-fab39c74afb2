package com.cricket.commentary;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for Live Cricket Commentary Service
 * 
 * This service is designed to handle 5 million concurrent users
 * with real-time ball-by-ball cricket commentary updates.
 * 
 * Key Features:
 * - Real-time WebSocket communication
 * - Multi-level caching (L1: Caffeine, L2: Redis)
 * - Optimized database queries
 * - Async processing for performance
 * - Comprehensive monitoring and health checks
 * 
 * Architecture:
 * - Spring Boot for rapid development and production-ready features
 * - MySQL for persistent storage with optimized schema
 * - Redis for high-performance caching
 * - WebSocket for real-time updates
 * - Micrometer for metrics and monitoring
 * 
 * <AUTHOR> Commentary Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class CommentaryApplication {

    public static void main(String[] args) {
        SpringApplication.run(CommentaryApplication.class, args);
    }
}
