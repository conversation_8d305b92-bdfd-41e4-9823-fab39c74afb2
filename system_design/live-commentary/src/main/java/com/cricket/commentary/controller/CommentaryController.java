package com.cricket.commentary.controller;

import com.cricket.commentary.dto.CommentaryDto;
import com.cricket.commentary.service.CommentaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Commentary operations
 * 
 * Provides endpoints for commentary management with real-time updates.
 * Optimized for high-frequency reads supporting 5M+ concurrent users.
 */
@RestController
@RequestMapping("/api/commentary")
@Tag(name = "Commentary Management", description = "APIs for managing ball-by-ball commentary")
@CrossOrigin(origins = "*") // Configure properly for production
public class CommentaryController {
    
    private final CommentaryService commentaryService;
    
    @Autowired
    public CommentaryController(CommentaryService commentaryService) {
        this.commentaryService = commentaryService;
    }
    
    /**
     * Add new commentary (Commentator workflow)
     * Critical endpoint for real-time updates
     */
    @PostMapping
    @Operation(summary = "Add new commentary", 
               description = "Adds ball-by-ball commentary with real-time broadcast")
    public ResponseEntity<CommentaryDto> addCommentary(@Valid @RequestBody CommentaryDto commentaryDto) {
        try {
            CommentaryDto createdCommentary = commentaryService.addCommentary(commentaryDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCommentary);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get recent commentary for a match (Reader workflow)
     * Most frequently called endpoint - heavily cached
     */
    @GetMapping("/matches/{matchId}/recent")
    @Operation(summary = "Get recent commentary", 
               description = "Returns the most recent commentary for a match (last 10 balls)")
    public ResponseEntity<List<CommentaryDto>> getRecentCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId) {
        List<CommentaryDto> commentary = commentaryService.getRecentCommentary(matchId);
        return ResponseEntity.ok(commentary);
    }
    
    /**
     * Get all commentary for a match with pagination
     */
    @GetMapping("/matches/{matchId}")
    @Operation(summary = "Get match commentary", 
               description = "Returns paginated commentary for a specific match")
    public ResponseEntity<Page<CommentaryDto>> getMatchCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<CommentaryDto> commentary = commentaryService.getMatchCommentary(matchId, page, size);
        return ResponseEntity.ok(commentary);
    }
    
    /**
     * Get commentary for a specific over
     */
    @GetMapping("/matches/{matchId}/overs/{overNumber}")
    @Operation(summary = "Get over commentary", 
               description = "Returns all commentary for a specific over")
    public ResponseEntity<List<CommentaryDto>> getOverCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId,
            @Parameter(description = "Over number") @PathVariable Integer overNumber) {
        
        List<CommentaryDto> commentary = commentaryService.getOverCommentary(matchId, overNumber);
        return ResponseEntity.ok(commentary);
    }
    
    /**
     * Get commentary for a specific ball
     */
    @GetMapping("/matches/{matchId}/overs/{overNumber}/balls/{ballNumber}")
    @Operation(summary = "Get ball commentary", 
               description = "Returns commentary for a specific ball")
    public ResponseEntity<CommentaryDto> getBallCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId,
            @Parameter(description = "Over number") @PathVariable Integer overNumber,
            @Parameter(description = "Ball number") @PathVariable Integer ballNumber) {
        
        Optional<CommentaryDto> commentary = commentaryService.getBallCommentary(matchId, overNumber, ballNumber);
        return commentary.map(ResponseEntity::ok)
                        .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get live commentary updates (for WebSocket synchronization)
     */
    @GetMapping("/matches/{matchId}/live")
    @Operation(summary = "Get live commentary updates", 
               description = "Returns commentary updates after a specific timestamp")
    public ResponseEntity<List<CommentaryDto>> getLiveCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId,
            @Parameter(description = "Get updates after this timestamp") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime after) {
        
        List<CommentaryDto> commentary = commentaryService.getCommentaryAfterTimestamp(matchId, after);
        return ResponseEntity.ok(commentary);
    }
    
    /**
     * Search commentary by text
     */
    @GetMapping("/search")
    @Operation(summary = "Search commentary", 
               description = "Search commentary by text content")
    public ResponseEntity<Page<CommentaryDto>> searchCommentary(
            @Parameter(description = "Search term") @RequestParam String q,
            @Parameter(description = "Filter by match ID") @RequestParam(required = false) Long matchId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        
        Page<CommentaryDto> commentary = commentaryService.searchCommentary(q, matchId, page, size);
        return ResponseEntity.ok(commentary);
    }
    
    /**
     * Get commentary statistics for a match
     */
    @GetMapping("/matches/{matchId}/statistics")
    @Operation(summary = "Get commentary statistics", 
               description = "Returns statistical information about match commentary")
    public ResponseEntity<CommentaryService.CommentaryStatistics> getCommentaryStatistics(
            @Parameter(description = "Match ID") @PathVariable Long matchId) {
        
        CommentaryService.CommentaryStatistics stats = commentaryService.getCommentaryStatistics(matchId);
        return ResponseEntity.ok(stats);
    }
}

/**
 * Separate controller for live commentary endpoint (optimized path)
 * This provides a simplified endpoint for the most common reader use case
 */
@RestController
@RequestMapping("/api/matches/{matchId}/commentary")
@Tag(name = "Live Commentary", description = "Optimized endpoints for live commentary access")
@CrossOrigin(origins = "*")
class LiveCommentaryController {
    
    private final CommentaryService commentaryService;
    
    @Autowired
    public LiveCommentaryController(CommentaryService commentaryService) {
        this.commentaryService = commentaryService;
    }
    
    /**
     * Optimized endpoint for live commentary (shortest possible path)
     */
    @GetMapping("/live")
    @Operation(summary = "Get live commentary (optimized)", 
               description = "Optimized endpoint for real-time commentary access")
    public ResponseEntity<List<CommentaryDto>> getLiveCommentary(
            @Parameter(description = "Match ID") @PathVariable Long matchId) {
        List<CommentaryDto> commentary = commentaryService.getRecentCommentary(matchId);
        return ResponseEntity.ok(commentary);
    }
}
