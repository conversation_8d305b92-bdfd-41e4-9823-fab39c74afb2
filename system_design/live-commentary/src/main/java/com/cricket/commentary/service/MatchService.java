package com.cricket.commentary.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cricket.commentary.dto.MatchDto;
import com.cricket.commentary.model.Match;
import com.cricket.commentary.model.MatchState;
import com.cricket.commentary.model.MatchStatus;
import com.cricket.commentary.repository.MatchRepository;
import com.cricket.commentary.repository.MatchStateRepository;

/**
 * Service class for Match operations
 * 
 * Implements business logic for match management with multi-level caching.
 * Optimized for high-read scenarios with 5M concurrent users.
 */
@Service
@Transactional(readOnly = true)
public class MatchService {
    
    private final MatchRepository matchRepository;
    private final MatchStateRepository matchStateRepository;
    
    @Autowired
    public MatchService(MatchRepository matchRepository, 
                       MatchStateRepository matchStateRepository) {
        this.matchRepository = matchRepository;
        this.matchStateRepository = matchStateRepository;
    }
    
    /**
     * Get all live matches with caching
     * Critical method - heavily cached for performance
     */
    @Cacheable(value = "liveMatches", unless = "#result.isEmpty()")
    public List<MatchDto> getLiveMatches() {
        List<Match> liveMatches = matchRepository.findLiveMatches();
        return liveMatches.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get match by ID with state information
     * Cached for frequent access by readers
     */
    @Cacheable(value = "matchDetails", key = "#matchId")
    public Optional<MatchDto> getMatchById(Long matchId) {
        Optional<Match> match = matchRepository.findById(matchId);
        if (match.isPresent()) {
            MatchDto dto = convertToDto(match.get());
            // Enrich with state information
            enrichWithState(dto);
            return Optional.of(dto);
        }
        return Optional.empty();
    }
    
    /**
     * Get matches by status with pagination
     */
    @Cacheable(value = "matchesByStatus", key = "#status + '_' + #page + '_' + #size")
    public Page<MatchDto> getMatchesByStatus(String status, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Match> matches = matchRepository.findByStatusOrderByMatchDateDesc(status, pageable);
        return matches.map(this::convertToDto);
    }
    
    /**
     * Search matches with caching
     */
    @Cacheable(value = "matchSearch", key = "#searchTerm + '_' + #status + '_' + #page + '_' + #size")
    public Page<MatchDto> searchMatches(String searchTerm, String status, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Match> matches = matchRepository.searchMatches(searchTerm, status, pageable);
        return matches.map(this::convertToDto);
    }
    
    /**
     * Get upcoming matches for today
     */
    @Cacheable(value = "todayMatches", unless = "#result.isEmpty()")
    public List<MatchDto> getTodayMatches() {
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        List<Match> matches = matchRepository.findMatchesOnDate(today);
        return matches.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Create new match
     */
    @Transactional
    @CacheEvict(value = {"liveMatches", "todayMatches", "matchesByStatus"}, allEntries = true)
    public MatchDto createMatch(MatchDto matchDto) {
        Match match = convertToEntity(matchDto);
        Match savedMatch = matchRepository.save(match);
        
        // Initialize match state
        MatchState matchState = new MatchState(savedMatch.getId());
        matchStateRepository.save(matchState);
        
        return convertToDto(savedMatch);
    }
    
    /**
     * Update match status
     */
    @Transactional
    @CacheEvict(value = {"liveMatches", "matchDetails", "matchesByStatus"}, allEntries = true)
    public MatchDto updateMatchStatus(Long matchId, String status) {
        Optional<Match> matchOpt = matchRepository.findById(matchId);
        if (matchOpt.isPresent()) {
            Match match = matchOpt.get();
            match.setStatus(MatchStatus.valueOf(status.toUpperCase()));
            Match updatedMatch = matchRepository.save(match);
            return convertToDto(updatedMatch);
        }
        throw new RuntimeException("Match not found with id: " + matchId);
    }
    
    /**
     * Check if match is live
     */
    @Cacheable(value = "matchStatus", key = "#matchId")
    public boolean isMatchLive(Long matchId) {
        return matchRepository.isMatchLive(matchId);
    }
    
    /**
     * Get match statistics
     */
    @Cacheable(value = "matchStats", unless = "#result == null")
    public MatchStatistics getMatchStatistics() {
        Object[] stats = matchRepository.getMatchStatistics();
        if (stats != null && stats.length >= 3) {
            return new MatchStatistics(
                ((Number) stats[0]).longValue(), // upcoming
                ((Number) stats[1]).longValue(), // live
                ((Number) stats[2]).longValue()  // completed
            );
        }
        return new MatchStatistics(0L, 0L, 0L);
    }
    
    /**
     * Get matches by team
     */
    @Cacheable(value = "teamMatches", key = "#team")
    public List<MatchDto> getMatchesByTeam(String team) {
        List<Match> matches = matchRepository.findMatchesByTeam(team);
        return matches.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get recent completed matches
     */
    @Cacheable(value = "recentMatches", key = "#page + '_' + #size")
    public Page<MatchDto> getRecentCompletedMatches(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Match> matches = matchRepository.findRecentCompletedMatches(pageable);
        return matches.map(this::convertToDto);
    }
    
    /**
     * Convert Match entity to DTO
     */
    private MatchDto convertToDto(Match match) {
        MatchDto dto = new MatchDto();
        dto.setId(match.getId());
        dto.setTeam1(match.getTeam1());
        dto.setTeam2(match.getTeam2());
        dto.setVenue(match.getVenue());
        dto.setMatchDate(match.getMatchDate());
        dto.setStatus(match.getStatus().toString().toLowerCase());
        dto.setCreatedAt(match.getCreatedAt());
        dto.setUpdatedAt(match.getUpdatedAt());
        return dto;
    }
    
    /**
     * Convert DTO to Match entity
     */
    private Match convertToEntity(MatchDto dto) {
        Match match = new Match();
        match.setTeam1(dto.getTeam1());
        match.setTeam2(dto.getTeam2());
        match.setVenue(dto.getVenue());
        match.setMatchDate(dto.getMatchDate());
        if (dto.getStatus() != null) {
            match.setStatus(MatchStatus.valueOf(dto.getStatus().toUpperCase()));
        }
        return match;
    }
    
    /**
     * Enrich DTO with match state information
     */
    private void enrichWithState(MatchDto dto) {
        if (dto.getId() != null) {
            Optional<MatchState> stateOpt = matchStateRepository.findByMatchId(dto.getId());
            if (stateOpt.isPresent()) {
                MatchState state = stateOpt.get();
                dto.setCurrentOver(state.getCurrentOver());
                dto.setCurrentBall(state.getCurrentBall());
                dto.setTotalBalls(state.getTotalBalls());
                
                // Calculate progress percentage (assuming 50 overs max)
                if (state.getTotalBalls() > 0) {
                    dto.setProgressPercentage(state.getProgressPercentage(50));
                }
            }
        }
    }
    
    /**
     * Statistics class for match data
     */
    public static class MatchStatistics {
        private final long upcomingMatches;
        private final long liveMatches;
        private final long completedMatches;
        
        public MatchStatistics(long upcoming, long live, long completed) {
            this.upcomingMatches = upcoming;
            this.liveMatches = live;
            this.completedMatches = completed;
        }
        
        public long getUpcomingMatches() { return upcomingMatches; }
        public long getLiveMatches() { return liveMatches; }
        public long getCompletedMatches() { return completedMatches; }
        public long getTotalMatches() { return upcomingMatches + liveMatches + completedMatches; }
    }
}
