package com.cricket.commentary.service;

import com.cricket.commentary.dto.CommentaryDto;
import com.cricket.commentary.model.Commentary;
import com.cricket.commentary.model.MatchState;
import com.cricket.commentary.repository.CommentaryRepository;
import com.cricket.commentary.repository.MatchStateRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Service class for Commentary operations
 * 
 * Implements high-performance commentary management with multi-level caching.
 * Optimized for 5M concurrent readers with real-time updates.
 */
@Service
@Transactional(readOnly = true)
public class CommentaryService {
    
    private final CommentaryRepository commentaryRepository;
    private final MatchStateRepository matchStateRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final WebSocketService webSocketService;
    
    // Cache configuration
    private static final String RECENT_COMMENTARY_KEY = "match:%d:recent_commentary";
    private static final String MATCH_STATE_KEY = "match:%d:state";
    private static final int RECENT_COMMENTARY_SIZE = 10;
    private static final int CACHE_TTL_MINUTES = 10;
    
    @Autowired
    public CommentaryService(CommentaryRepository commentaryRepository,
                           MatchStateRepository matchStateRepository,
                           RedisTemplate<String, Object> redisTemplate,
                           WebSocketService webSocketService) {
        this.commentaryRepository = commentaryRepository;
        this.matchStateRepository = matchStateRepository;
        this.redisTemplate = redisTemplate;
        this.webSocketService = webSocketService;
    }
    
    /**
     * Add new commentary with atomic state update
     * Critical method for commentator workflow
     */
    @Transactional
    @CacheEvict(value = {"recentCommentary", "matchCommentary"}, key = "#commentaryDto.matchId")
    public CommentaryDto addCommentary(CommentaryDto commentaryDto) {
        // Validate ball doesn't already exist
        if (commentaryRepository.existsByMatchIdAndOverNumberAndBallNumber(
                commentaryDto.getMatchId(), 
                commentaryDto.getOverNumber(), 
                commentaryDto.getBallNumber())) {
            throw new RuntimeException("Commentary already exists for this ball");
        }
        
        // Create and save commentary
        Commentary commentary = convertToEntity(commentaryDto);
        Commentary savedCommentary = commentaryRepository.save(commentary);
        
        // Update match state atomically
        updateMatchState(savedCommentary);
        
        // Update Redis cache
        updateRedisCache(savedCommentary);
        
        // Broadcast to WebSocket clients
        CommentaryDto result = convertToDto(savedCommentary);
        webSocketService.broadcastCommentary(result);
        
        return result;
    }
    
    /**
     * Get recent commentary for a match (cached)
     * Most frequently called method - heavily optimized
     */
    @Cacheable(value = "recentCommentary", key = "#matchId")
    public List<CommentaryDto> getRecentCommentary(Long matchId) {
        // Try Redis cache first
        String cacheKey = String.format(RECENT_COMMENTARY_KEY, matchId);
        List<CommentaryDto> cached = (List<CommentaryDto>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cached != null && !cached.isEmpty()) {
            return cached;
        }
        
        // Fallback to database
        Pageable pageable = PageRequest.of(0, RECENT_COMMENTARY_SIZE);
        Page<Commentary> commentaryPage = commentaryRepository.findRecentCommentaryByMatchId(matchId, pageable);
        List<CommentaryDto> result = commentaryPage.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        // Cache in Redis
        redisTemplate.opsForValue().set(cacheKey, result, CACHE_TTL_MINUTES, TimeUnit.MINUTES);
        
        return result;
    }
    
    /**
     * Get all commentary for a match with pagination
     */
    @Cacheable(value = "matchCommentary", key = "#matchId + '_' + #page + '_' + #size")
    public Page<CommentaryDto> getMatchCommentary(Long matchId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Commentary> commentaryPage = commentaryRepository.findRecentCommentaryByMatchId(matchId, pageable);
        return commentaryPage.map(this::convertToDto);
    }
    
    /**
     * Get commentary for a specific over
     */
    @Cacheable(value = "overCommentary", key = "#matchId + '_' + #overNumber")
    public List<CommentaryDto> getOverCommentary(Long matchId, Integer overNumber) {
        List<Commentary> commentary = commentaryRepository.findByMatchIdAndOverNumber(matchId, overNumber);
        return commentary.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get commentary for a specific ball
     */
    @Cacheable(value = "ballCommentary", key = "#matchId + '_' + #overNumber + '_' + #ballNumber")
    public Optional<CommentaryDto> getBallCommentary(Long matchId, Integer overNumber, Integer ballNumber) {
        Optional<Commentary> commentary = commentaryRepository.findByMatchIdAndOverNumberAndBallNumber(
                matchId, overNumber, ballNumber);
        return commentary.map(this::convertToDto);
    }
    
    /**
     * Get live commentary updates after a timestamp
     * Used for WebSocket synchronization
     */
    public List<CommentaryDto> getCommentaryAfterTimestamp(Long matchId, LocalDateTime timestamp) {
        List<Commentary> commentary = commentaryRepository.findByMatchIdAfterTimestamp(matchId, timestamp);
        return commentary.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Get commentary statistics for a match
     */
    @Cacheable(value = "commentaryStats", key = "#matchId")
    public CommentaryStatistics getCommentaryStatistics(Long matchId) {
        Object[] stats = commentaryRepository.getCommentaryStatistics(matchId);
        if (stats != null && stats.length >= 5) {
            return new CommentaryStatistics(
                ((Number) stats[0]).longValue(),  // totalBalls
                ((Number) stats[1]).intValue(),   // maxOver
                ((Number) stats[2]).intValue(),   // maxBall
                (LocalDateTime) stats[3],         // firstBall
                (LocalDateTime) stats[4]          // lastBall
            );
        }
        return new CommentaryStatistics(0L, 0, 0, null, null);
    }
    
    /**
     * Search commentary by text
     */
    public Page<CommentaryDto> searchCommentary(String searchTerm, Long matchId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Commentary> commentary = commentaryRepository.searchCommentaryByText(searchTerm, matchId, pageable);
        return commentary.map(this::convertToDto);
    }
    
    /**
     * Update match state atomically
     */
    private void updateMatchState(Commentary commentary) {
        int totalBalls = (commentary.getOverNumber() - 1) * 6 + commentary.getBallNumber();
        
        int updated = matchStateRepository.upsertMatchState(
                commentary.getMatchId(),
                commentary.getOverNumber(),
                commentary.getBallNumber(),
                totalBalls,
                commentary.getId(),
                commentary.getTimestamp()
        );
        
        if (updated == 0) {
            throw new RuntimeException("Failed to update match state");
        }
        
        // Invalidate match state cache
        String stateKey = String.format(MATCH_STATE_KEY, commentary.getMatchId());
        redisTemplate.delete(stateKey);
    }
    
    /**
     * Update Redis cache with new commentary
     */
    private void updateRedisCache(Commentary commentary) {
        String cacheKey = String.format(RECENT_COMMENTARY_KEY, commentary.getMatchId());
        
        // Get current cached list
        List<CommentaryDto> cached = (List<CommentaryDto>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cached == null) {
            // Create new list
            cached = List.of(convertToDto(commentary));
        } else {
            // Add new commentary to the beginning and limit size
            cached = cached.stream()
                    .limit(RECENT_COMMENTARY_SIZE - 1)
                    .collect(Collectors.toList());
            cached.add(0, convertToDto(commentary));
        }
        
        // Update cache
        redisTemplate.opsForValue().set(cacheKey, cached, CACHE_TTL_MINUTES, TimeUnit.MINUTES);
    }
    
    /**
     * Convert Commentary entity to DTO
     */
    private CommentaryDto convertToDto(Commentary commentary) {
        CommentaryDto dto = new CommentaryDto();
        dto.setId(commentary.getId());
        dto.setMatchId(commentary.getMatchId());
        dto.setOverNumber(commentary.getOverNumber());
        dto.setBallNumber(commentary.getBallNumber());
        dto.setCommentaryText(commentary.getCommentaryText());
        dto.setCommentatorId(commentary.getCommentatorId());
        dto.setTimestamp(commentary.getTimestamp());
        dto.setBallIdentifier(commentary.getBallIdentifier());
        dto.setTotalBalls(commentary.getTotalBalls());
        return dto;
    }
    
    /**
     * Convert DTO to Commentary entity
     */
    private Commentary convertToEntity(CommentaryDto dto) {
        Commentary commentary = new Commentary();
        commentary.setMatchId(dto.getMatchId());
        commentary.setOverNumber(dto.getOverNumber());
        commentary.setBallNumber(dto.getBallNumber());
        commentary.setCommentaryText(dto.getCommentaryText());
        commentary.setCommentatorId(dto.getCommentatorId());
        if (dto.getTimestamp() != null) {
            commentary.setTimestamp(dto.getTimestamp());
        }
        return commentary;
    }
    
    /**
     * Statistics class for commentary data
     */
    public static class CommentaryStatistics {
        private final long totalBalls;
        private final int maxOver;
        private final int maxBall;
        private final LocalDateTime firstBall;
        private final LocalDateTime lastBall;
        
        public CommentaryStatistics(long totalBalls, int maxOver, int maxBall, 
                                  LocalDateTime firstBall, LocalDateTime lastBall) {
            this.totalBalls = totalBalls;
            this.maxOver = maxOver;
            this.maxBall = maxBall;
            this.firstBall = firstBall;
            this.lastBall = lastBall;
        }
        
        public long getTotalBalls() { return totalBalls; }
        public int getMaxOver() { return maxOver; }
        public int getMaxBall() { return maxBall; }
        public LocalDateTime getFirstBall() { return firstBall; }
        public LocalDateTime getLastBall() { return lastBall; }
    }
}
