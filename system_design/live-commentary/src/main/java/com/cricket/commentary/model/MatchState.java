package com.cricket.commentary.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Entity representing the current state of a cricket match
 * 
 * This entity provides quick access to match progress without
 * scanning the entire commentary table. It's updated atomically
 * with each new commentary entry.
 */
@Entity
@Table(name = "match_state", indexes = {
    @Index(name = "idx_last_updated", columnList = "lastUpdated")
})
public class MatchState {
    
    @Id
    @Column(name = "match_id")
    private Long matchId;
    
    @NotNull(message = "Current over is required")
    @Min(value = 0, message = "Current over must be non-negative")
    @Column(name = "current_over", nullable = false)
    private Integer currentOver = 0;
    
    @NotNull(message = "Current ball is required")
    @Min(value = 0, message = "Current ball must be non-negative")
    @Column(name = "current_ball", nullable = false)
    private Integer currentBall = 0;
    
    @NotNull(message = "Total balls is required")
    @Min(value = 0, message = "Total balls must be non-negative")
    @Column(name = "total_balls", nullable = false)
    private Integer totalBalls = 0;
    
    @Column(name = "last_commentary_id")
    private Long lastCommentaryId;
    
    @Column(name = "last_updated", nullable = false)
    private LocalDateTime lastUpdated;
    
    // Constructors
    public MatchState() {}
    
    public MatchState(Long matchId) {
        this.matchId = matchId;
        this.currentOver = 0;
        this.currentBall = 0;
        this.totalBalls = 0;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public MatchState(Long matchId, Integer currentOver, Integer currentBall) {
        this.matchId = matchId;
        this.currentOver = currentOver;
        this.currentBall = currentBall;
        this.totalBalls = calculateTotalBalls(currentOver, currentBall);
        this.lastUpdated = LocalDateTime.now();
    }
    
    // Lifecycle callbacks
    @PrePersist
    @PreUpdate
    protected void onUpdate() {
        lastUpdated = LocalDateTime.now();
        if (totalBalls == null || totalBalls == 0) {
            totalBalls = calculateTotalBalls(currentOver, currentBall);
        }
    }
    
    // Getters and Setters
    public Long getMatchId() {
        return matchId;
    }
    
    public void setMatchId(Long matchId) {
        this.matchId = matchId;
    }
    
    public Integer getCurrentOver() {
        return currentOver;
    }
    
    public void setCurrentOver(Integer currentOver) {
        this.currentOver = currentOver;
        updateTotalBalls();
    }
    
    public Integer getCurrentBall() {
        return currentBall;
    }
    
    public void setCurrentBall(Integer currentBall) {
        this.currentBall = currentBall;
        updateTotalBalls();
    }
    
    public Integer getTotalBalls() {
        return totalBalls;
    }
    
    public void setTotalBalls(Integer totalBalls) {
        this.totalBalls = totalBalls;
    }
    
    public Long getLastCommentaryId() {
        return lastCommentaryId;
    }
    
    public void setLastCommentaryId(Long lastCommentaryId) {
        this.lastCommentaryId = lastCommentaryId;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    // Business methods
    public void updateState(Integer overNumber, Integer ballNumber, Long commentaryId) {
        this.currentOver = overNumber;
        this.currentBall = ballNumber;
        this.totalBalls = calculateTotalBalls(overNumber, ballNumber);
        this.lastCommentaryId = commentaryId;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getCurrentBallIdentifier() {
        return currentOver + "." + currentBall;
    }
    
    public double getProgressPercentage(int totalOvers) {
        if (totalOvers <= 0) return 0.0;
        int maxBalls = totalOvers * 6;
        return Math.min(100.0, (totalBalls * 100.0) / maxBalls);
    }
    
    public boolean isMatchStarted() {
        return totalBalls > 0;
    }
    
    public boolean isOverComplete() {
        return currentBall == 6;
    }
    
    private void updateTotalBalls() {
        this.totalBalls = calculateTotalBalls(currentOver, currentBall);
    }
    
    private Integer calculateTotalBalls(Integer over, Integer ball) {
        if (over == null || ball == null) return 0;
        return Math.max(0, (over - 1) * 6 + ball);
    }
    
    // toString, equals, hashCode
    @Override
    public String toString() {
        return "MatchState{" +
                "matchId=" + matchId +
                ", currentOver=" + currentOver +
                ", currentBall=" + currentBall +
                ", totalBalls=" + totalBalls +
                ", lastCommentaryId=" + lastCommentaryId +
                ", lastUpdated=" + lastUpdated +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MatchState)) return false;
        MatchState that = (MatchState) o;
        return matchId != null && matchId.equals(that.matchId);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
