package com.cricket.commentary.repository;

import com.cricket.commentary.model.MatchState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MatchState entity
 * 
 * Provides atomic operations for match state management.
 * Critical for maintaining consistency between commentary and match state.
 */
@Repository
public interface MatchStateRepository extends JpaRepository<MatchState, Long> {
    
    /**
     * Find match state by match ID
     * Primary lookup method - heavily cached
     */
    Optional<MatchState> findByMatchId(Long matchId);
    
    /**
     * Update match state atomically
     * Used when new commentary is added
     */
    @Modifying
    @Transactional
    @Query("UPDATE MatchState ms SET " +
           "ms.currentOver = :overNumber, " +
           "ms.currentBall = :ballNumber, " +
           "ms.totalBalls = :totalBalls, " +
           "ms.lastCommentaryId = :commentaryId, " +
           "ms.lastUpdated = :timestamp " +
           "WHERE ms.matchId = :matchId")
    int updateMatchState(@Param("matchId") Long matchId,
                        @Param("overNumber") Integer overNumber,
                        @Param("ballNumber") Integer ballNumber,
                        @Param("totalBalls") Integer totalBalls,
                        @Param("commentaryId") Long commentaryId,
                        @Param("timestamp") LocalDateTime timestamp);
    
    /**
     * Create or update match state
     * Upsert operation for initial state creation
     */
    @Modifying
    @Transactional
    @Query(value = "INSERT INTO match_state (match_id, current_over, current_ball, " +
                   "total_balls, last_commentary_id, last_updated) " +
                   "VALUES (:matchId, :overNumber, :ballNumber, :totalBalls, " +
                   ":commentaryId, :timestamp) " +
                   "ON DUPLICATE KEY UPDATE " +
                   "current_over = :overNumber, " +
                   "current_ball = :ballNumber, " +
                   "total_balls = :totalBalls, " +
                   "last_commentary_id = :commentaryId, " +
                   "last_updated = :timestamp",
           nativeQuery = true)
    int upsertMatchState(@Param("matchId") Long matchId,
                        @Param("overNumber") Integer overNumber,
                        @Param("ballNumber") Integer ballNumber,
                        @Param("totalBalls") Integer totalBalls,
                        @Param("commentaryId") Long commentaryId,
                        @Param("timestamp") LocalDateTime timestamp);
    
    /**
     * Find states for live matches
     * Used for dashboard and monitoring
     */
    @Query("SELECT ms FROM MatchState ms " +
           "JOIN Match m ON ms.matchId = m.id " +
           "WHERE m.status = 'LIVE' " +
           "ORDER BY ms.lastUpdated DESC")
    List<MatchState> findLiveMatchStates();
    
    /**
     * Find recently updated match states
     * Used for real-time updates and monitoring
     */
    @Query("SELECT ms FROM MatchState ms " +
           "WHERE ms.lastUpdated >= :since " +
           "ORDER BY ms.lastUpdated DESC")
    List<MatchState> findRecentlyUpdated(@Param("since") LocalDateTime since);
    
    /**
     * Find match states that haven't been updated recently
     * Used for detecting stale matches
     */
    @Query("SELECT ms FROM MatchState ms " +
           "JOIN Match m ON ms.matchId = m.id " +
           "WHERE m.status = 'LIVE' " +
           "AND ms.lastUpdated < :cutoffTime " +
           "ORDER BY ms.lastUpdated ASC")
    List<MatchState> findStaleMatchStates(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * Get current ball information for a match
     * Optimized for frequent access
     */
    @Query("SELECT ms.currentOver, ms.currentBall, ms.totalBalls " +
           "FROM MatchState ms WHERE ms.matchId = :matchId")
    Object[] getCurrentBallInfo(@Param("matchId") Long matchId);
    
    /**
     * Check if match has started (has any balls)
     */
    @Query("SELECT CASE WHEN ms.totalBalls > 0 THEN true ELSE false END " +
           "FROM MatchState ms WHERE ms.matchId = :matchId")
    Boolean isMatchStarted(@Param("matchId") Long matchId);
    
    /**
     * Get match progress percentage
     * Used for UI progress indicators
     */
    @Query("SELECT (ms.totalBalls * 100.0) / (:totalOvers * 6) as progress " +
           "FROM MatchState ms WHERE ms.matchId = :matchId")
    Double getMatchProgress(@Param("matchId") Long matchId, 
                           @Param("totalOvers") Integer totalOvers);
    
    /**
     * Find matches by current over range
     * Used for analytics and reporting
     */
    @Query("SELECT ms FROM MatchState ms " +
           "WHERE ms.currentOver BETWEEN :minOver AND :maxOver " +
           "ORDER BY ms.currentOver ASC, ms.currentBall ASC")
    List<MatchState> findByOverRange(@Param("minOver") Integer minOver,
                                    @Param("maxOver") Integer maxOver);
    
    /**
     * Reset match state (for testing or corrections)
     */
    @Modifying
    @Transactional
    @Query("UPDATE MatchState ms SET " +
           "ms.currentOver = 0, " +
           "ms.currentBall = 0, " +
           "ms.totalBalls = 0, " +
           "ms.lastCommentaryId = NULL, " +
           "ms.lastUpdated = CURRENT_TIMESTAMP " +
           "WHERE ms.matchId = :matchId")
    int resetMatchState(@Param("matchId") Long matchId);
    
    /**
     * Delete match state (cleanup)
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM MatchState ms WHERE ms.matchId = :matchId")
    int deleteByMatchId(@Param("matchId") Long matchId);
    
    /**
     * Get statistics for all match states
     * Used for monitoring and analytics
     */
    @Query("SELECT " +
           "COUNT(ms) as totalMatches, " +
           "AVG(ms.totalBalls) as avgBalls, " +
           "MAX(ms.totalBalls) as maxBalls, " +
           "MIN(ms.lastUpdated) as oldestUpdate, " +
           "MAX(ms.lastUpdated) as newestUpdate " +
           "FROM MatchState ms")
    Object[] getMatchStateStatistics();
    
    /**
     * Find match states with specific ball counts
     * Used for milestone tracking
     */
    @Query("SELECT ms FROM MatchState ms " +
           "WHERE ms.totalBalls IN :ballCounts " +
           "ORDER BY ms.lastUpdated DESC")
    List<MatchState> findByTotalBallsIn(@Param("ballCounts") List<Integer> ballCounts);
    
    /**
     * Bulk update last updated timestamp
     * Used for maintenance operations
     */
    @Modifying
    @Transactional
    @Query("UPDATE MatchState ms SET ms.lastUpdated = CURRENT_TIMESTAMP " +
           "WHERE ms.matchId IN :matchIds")
    int updateLastUpdatedBulk(@Param("matchIds") List<Long> matchIds);
}
