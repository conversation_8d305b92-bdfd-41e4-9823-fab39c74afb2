-- Cricket Commentary Database Schema
-- Optimized for high-read, low-write workload with 5M concurrent users

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS cricket_commentary;
USE cricket_commentary;

-- Matches table - stores match information
CREATE TABLE IF NOT EXISTS matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    team1 VARCHAR(100) NOT NULL,
    team2 VARCHAR(100) NOT NULL,
    venue VARCHAR(200),
    match_date DATETIME NOT NULL,
    status ENUM('upcoming', 'live', 'completed') NOT NULL DEFAULT 'upcoming',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for efficient querying
    INDEX idx_status_date (status, match_date),
    INDEX idx_match_date (match_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Commentary table - stores ball-by-ball commentary
CREATE TABLE IF NOT EXISTS commentary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    match_id BIGINT NOT NULL,
    over_number INT NOT NULL,
    ball_number INT NOT NULL,
    commentary_text TEXT NOT NULL,
    commentator_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE,
    
    -- Indexes for efficient querying
    INDEX idx_match_over_ball (match_id, over_number, ball_number),
    INDEX idx_match_timestamp (match_id, timestamp),
    INDEX idx_timestamp (timestamp),
    INDEX idx_commentator (commentator_id),
    
    -- Unique constraint to prevent duplicate entries
    UNIQUE KEY uk_match_over_ball (match_id, over_number, ball_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Match state table - stores current state for quick access
CREATE TABLE IF NOT EXISTS match_state (
    match_id BIGINT PRIMARY KEY,
    current_over INT NOT NULL DEFAULT 0,
    current_ball INT NOT NULL DEFAULT 0,
    total_balls INT NOT NULL DEFAULT 0,
    last_commentary_id BIGINT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE,
    FOREIGN KEY (last_commentary_id) REFERENCES commentary(id) ON DELETE SET NULL,
    
    -- Index for quick lookups
    INDEX idx_last_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Commentators table - stores commentator information
CREATE TABLE IF NOT EXISTS commentators (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Index for active commentators
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data for testing
INSERT INTO commentators (id, name, email) VALUES 
('john_doe', 'John Doe', '<EMAIL>'),
('jane_smith', 'Jane Smith', '<EMAIL>'),
('mike_wilson', 'Mike Wilson', '<EMAIL>')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- Sample match data
INSERT INTO matches (team1, team2, venue, match_date, status) VALUES 
('India', 'Australia', 'Melbourne Cricket Ground', '2024-01-15 14:30:00', 'upcoming'),
('England', 'New Zealand', 'Lords Cricket Ground', '2024-01-16 10:00:00', 'upcoming'),
('Pakistan', 'South Africa', 'Karachi National Stadium', '2024-01-17 15:00:00', 'upcoming')
ON DUPLICATE KEY UPDATE team1=VALUES(team1);

-- Initialize match state for sample matches
INSERT INTO match_state (match_id, current_over, current_ball, total_balls) 
SELECT id, 0, 0, 0 FROM matches 
ON DUPLICATE KEY UPDATE current_over=VALUES(current_over);

-- Performance optimization views
CREATE OR REPLACE VIEW live_matches AS
SELECT 
    m.id,
    m.team1,
    m.team2,
    m.venue,
    m.match_date,
    m.status,
    ms.current_over,
    ms.current_ball,
    ms.total_balls,
    ms.last_updated
FROM matches m
LEFT JOIN match_state ms ON m.id = ms.match_id
WHERE m.status = 'live';

-- View for recent commentary (last 10 balls)
CREATE OR REPLACE VIEW recent_commentary AS
SELECT 
    c.id,
    c.match_id,
    c.over_number,
    c.ball_number,
    c.commentary_text,
    c.commentator_id,
    c.timestamp,
    cm.name as commentator_name
FROM commentary c
JOIN commentators cm ON c.commentator_id = cm.id
WHERE c.id IN (
    SELECT id FROM commentary c2 
    WHERE c2.match_id = c.match_id 
    ORDER BY c2.timestamp DESC 
    LIMIT 10
)
ORDER BY c.timestamp DESC;

-- Stored procedure for adding commentary with state update
DELIMITER //
CREATE PROCEDURE AddCommentary(
    IN p_match_id BIGINT,
    IN p_over_number INT,
    IN p_ball_number INT,
    IN p_commentary_text TEXT,
    IN p_commentator_id VARCHAR(50)
)
BEGIN
    DECLARE commentary_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Insert commentary
    INSERT INTO commentary (match_id, over_number, ball_number, commentary_text, commentator_id)
    VALUES (p_match_id, p_over_number, p_ball_number, p_commentary_text, p_commentator_id);
    
    SET commentary_id = LAST_INSERT_ID();
    
    -- Update match state
    INSERT INTO match_state (match_id, current_over, current_ball, total_balls, last_commentary_id)
    VALUES (p_match_id, p_over_number, p_ball_number, 
            (p_over_number * 6 + p_ball_number), commentary_id)
    ON DUPLICATE KEY UPDATE
        current_over = p_over_number,
        current_ball = p_ball_number,
        total_balls = (p_over_number * 6 + p_ball_number),
        last_commentary_id = commentary_id,
        last_updated = CURRENT_TIMESTAMP;
    
    COMMIT;
    
    SELECT commentary_id as id;
END //
DELIMITER ;

-- Function to get match progress percentage
DELIMITER //
CREATE FUNCTION GetMatchProgress(p_match_id BIGINT, p_total_overs INT)
RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_balls INT DEFAULT 0;
    DECLARE total_balls INT;
    
    SELECT COALESCE(ms.total_balls, 0) INTO current_balls
    FROM match_state ms
    WHERE ms.match_id = p_match_id;
    
    SET total_balls = p_total_overs * 6;
    
    IF total_balls = 0 THEN
        RETURN 0.00;
    END IF;
    
    RETURN ROUND((current_balls / total_balls) * 100, 2);
END //
DELIMITER ;

-- Create indexes for performance optimization
-- These indexes are designed for the specific query patterns of the application

-- Composite index for commentary retrieval by match and time
CREATE INDEX idx_commentary_match_time ON commentary(match_id, timestamp DESC);

-- Index for commentator performance analysis
CREATE INDEX idx_commentary_commentator_time ON commentary(commentator_id, timestamp DESC);

-- Covering index for match listing with state
CREATE INDEX idx_matches_status_date_covering ON matches(status, match_date, id, team1, team2);

-- Analyze tables for query optimization
ANALYZE TABLE matches;
ANALYZE TABLE commentary;
ANALYZE TABLE match_state;
ANALYZE TABLE commentators;
