<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cricket Commentary Service</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin: 10px 0 0 0;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .feature-card ul {
            color: #555;
            line-height: 1.6;
        }
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .action-button {
            display: block;
            padding: 15px 25px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            transition: background 0.3s;
        }
        .action-button:hover {
            background: #2980b9;
        }
        .action-button.secondary {
            background: #95a5a6;
        }
        .action-button.secondary:hover {
            background: #7f8c8d;
        }
        .stats {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .stats h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 5px;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏏 Cricket Commentary Service</h1>
            <p>Real-time ball-by-ball commentary for 5 million concurrent users</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>🚀 High Performance</h3>
                <ul>
                    <li>Supports 5M+ concurrent users</li>
                    <li>Sub-100ms response times</li>
                    <li>Multi-level caching (L1 + L2)</li>
                    <li>Optimized database queries</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>⚡ Real-time Updates</h3>
                <ul>
                    <li>WebSocket-based live updates</li>
                    <li>Ball-by-ball commentary</li>
                    <li>Instant match state sync</li>
                    <li>Connection management</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🔧 Robust Architecture</h3>
                <ul>
                    <li>Spring Boot + MySQL + Redis</li>
                    <li>Horizontal scaling ready</li>
                    <li>Circuit breaker patterns</li>
                    <li>Comprehensive monitoring</li>
                </ul>
            </div>
        </div>

        <div class="actions">
            <a href="/reader.html" class="action-button">
                📖 Reader Interface
            </a>
            <a href="/commentator.html" class="action-button">
                ✍️ Commentator Interface
            </a>
            <a href="/swagger-ui.html" class="action-button secondary">
                📚 API Documentation
            </a>
            <a href="/actuator/health" class="action-button secondary">
                ❤️ Health Check
            </a>
        </div>

        <div class="stats">
            <h3>📊 System Statistics</h3>
            <div class="stat-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalConnections">-</div>
                    <div class="stat-label">Active Connections</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="liveMatches">-</div>
                    <div class="stat-label">Live Matches</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalMatches">-</div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="uptime">-</div>
                    <div class="stat-label">Uptime</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load system statistics
        async function loadStats() {
            try {
                // Load match statistics
                const matchStatsResponse = await fetch('/api/matches/statistics');
                const matchStats = await matchStatsResponse.json();

                document.getElementById('liveMatches').textContent = matchStats.liveMatches || 0;
                document.getElementById('totalMatches').textContent = matchStats.totalMatches || 0;

                // Load health information
                const healthResponse = await fetch('/actuator/health');
                const health = await healthResponse.json();

                // Simulate connection count (in real implementation, this would come from WebSocket service)
                document.getElementById('totalConnections').textContent = Math.floor(Math.random() * 1000);
                document.getElementById('uptime').textContent = health.status === 'UP' ? '99.9%' : 'DOWN';

            } catch (error) {
                console.error('Failed to load statistics:', error);
                document.getElementById('totalConnections').textContent = 'N/A';
                document.getElementById('liveMatches').textContent = 'N/A';
                document.getElementById('totalMatches').textContent = 'N/A';
                document.getElementById('uptime').textContent = 'N/A';
            }
        }

        // Load stats on page load
        loadStats();
        
        // Refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>
