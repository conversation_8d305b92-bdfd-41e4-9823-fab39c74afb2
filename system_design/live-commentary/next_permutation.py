"""
Next Permutation Problem Solution

The algorithm to find the next lexicographically greater permutation:

1. Find the largest index i such that nums[i] < nums[i + 1]
   If no such index exists, the permutation is the last permutation.

2. Find the largest index j greater than i such that nums[i] < nums[j]

3. Swap the value of nums[i] with that of nums[j]

4. Reverse the suffix starting at nums[i + 1]

Time Complexity: O(n)
Space Complexity: O(1)
"""

def nextPermutation(nums):
    """
    Finds the next permutation of the given array in-place.
    
    Args:
        nums: List[int] - Array of integers to find next permutation for
    
    Returns:
        None - Modifies the array in-place
    """
    n = len(nums)
    
    # Step 1: Find the largest index i such that nums[i] < nums[i + 1]
    i = n - 2
    while i >= 0 and nums[i] >= nums[i + 1]:
        i -= 1
    
    # If no such index exists, reverse the entire array (it's the last permutation)
    if i == -1:
        nums.reverse()
        return
    
    # Step 2: Find the largest index j > i such that nums[i] < nums[j]
    j = n - 1
    while nums[j] <= nums[i]:
        j -= 1
    
    # Step 3: Swap nums[i] and nums[j]
    nums[i], nums[j] = nums[j], nums[i]
    
    # Step 4: Reverse the suffix starting at nums[i + 1]
    nums[i + 1:] = reversed(nums[i + 1:])


def nextPermutationDetailed(nums):
    """
    Alternative implementation with detailed comments and step-by-step explanation.
    """
    print(f"Input: {nums}")
    n = len(nums)
    
    # Step 1: Find the rightmost character that is smaller than its next character
    i = n - 2
    while i >= 0 and nums[i] >= nums[i + 1]:
        i -= 1
    
    print(f"Step 1: Found pivot at index {i}")
    
    if i == -1:
        # The array is in descending order, so it's the last permutation
        print("Array is the last permutation, reversing to get first permutation")
        nums.reverse()
        print(f"Output: {nums}")
        return
    
    # Step 2: Find the smallest character on right side of above character 
    # that is greater than above character
    j = n - 1
    while nums[j] <= nums[i]:
        j -= 1
    
    print(f"Step 2: Found swap target at index {j}")
    print(f"Swapping nums[{i}]={nums[i]} with nums[{j}]={nums[j]}")
    
    # Step 3: Swap the found characters
    nums[i], nums[j] = nums[j], nums[i]
    
    print(f"After swap: {nums}")
    
    # Step 4: Reverse the substring after the original pivot
    print(f"Step 4: Reversing suffix from index {i + 1}")
    nums[i + 1:] = reversed(nums[i + 1:])
    
    print(f"Output: {nums}")


def test_next_permutation():
    """Test cases for the next permutation function."""
    
    test_cases = [
        [1, 2, 3],      # Expected: [1, 3, 2]
        [3, 2, 1],      # Expected: [1, 2, 3]
        [1, 1, 5],      # Expected: [1, 5, 1]
        [1, 3, 2],      # Expected: [2, 1, 3]
        [2, 3, 1],      # Expected: [3, 1, 2]
        [1],            # Expected: [1]
        [1, 2],         # Expected: [2, 1]
        [2, 1],         # Expected: [1, 2]
        [1, 5, 1],      # Expected: [5, 1, 1]
        [5, 4, 7, 5, 3, 2]  # Expected: [5, 5, 2, 3, 4, 7]
    ]
    
    print("Testing Next Permutation Algorithm")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases):
        original = test_case.copy()
        print(f"\nTest Case {i + 1}:")
        nextPermutationDetailed(test_case)
        print(f"Original: {original} -> Next: {test_case}")


if __name__ == "__main__":
    # Run basic tests
    test_next_permutation()
    
    print("\n" + "=" * 50)
    print("ALGORITHM EXPLANATION")
    print("=" * 50)
    print("""
    The Next Permutation algorithm works as follows:
    
    1. Find the largest index i such that nums[i] < nums[i + 1]
       - This finds the rightmost character that can be increased
       - If no such index exists, we have the last permutation
    
    2. Find the largest index j > i such that nums[i] < nums[j]
       - This finds the smallest number greater than nums[i] to swap with
    
    3. Swap nums[i] and nums[j]
       - This increases the permutation at position i
    
    4. Reverse the suffix starting at nums[i + 1]
       - This ensures we get the lexicographically smallest arrangement
         for the suffix, making it the immediate next permutation
    
    Example: [1, 2, 3] -> [1, 3, 2]
    - i = 1 (nums[1] = 2 < nums[2] = 3)
    - j = 2 (nums[2] = 3 > nums[1] = 2)
    - Swap: [1, 3, 2]
    - Reverse suffix: [1, 3, 2] (no change needed)
    
    Example: [2, 3, 1] -> [3, 1, 2]
    - i = 0 (nums[0] = 2 < nums[1] = 3)
    - j = 1 (nums[1] = 3 > nums[0] = 2)
    - Swap: [3, 2, 1]
    - Reverse suffix: [3, 1, 2]
    """)
