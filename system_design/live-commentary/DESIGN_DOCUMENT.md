# Live Cricket Commentary Service - Design Document

## 1. Problem Statement & Requirements

### Core Requirements
- **Real-time Commentary**: Ball-by-ball updates by professional commentators
- **Scale**: 5 million concurrent readers
- **Low Latency**: Minimal time to serve commentary
- **High Availability**: 99.9% uptime
- **Data Durability**: No data loss under any circumstances

### Extended Requirements
- **Commentator Workflow**: Easy interface for professional commentators
- **Reader Workflow**: Fast, responsive reading experience
- **Cost Effectiveness**: Optimized resource utilization
- **Scalability**: Handle traffic spikes during popular matches
- **Consistency**: No data inconsistencies
- **Performance**: No deadlocks, minimal locking impact

## 2. Capacity Planning

### Traffic Estimation
- **Peak Concurrent Readers**: 5 million
- **Read QPS**: 5M users × 1 request/10 seconds = 500K QPS
- **Write QPS**: 1 commentator × 1 update/30 seconds = 0.033 QPS
- **Data Size**: ~200 bytes per commentary entry
- **Daily Commentary**: ~300 balls × 200 bytes = 60KB per match

### Storage Requirements
- **Active Match Data**: 60KB (in-memory cache)
- **Historical Data**: 365 matches × 60KB = ~22MB/year
- **Cache Memory**: 5M connections × 1KB session = 5GB
- **Database**: MySQL with 100GB storage (10 years of matches)

## 3. High-Level Architecture

```
[Commentator] → [Admin API] → [Message Queue] → [Cache] → [Reader API] → [Users]
                     ↓              ↓            ↓
                [Database] ← [Background Sync] ← [Redis]
```

### Core Components

#### 3.1 API Gateway & Load Balancer
- **Technology**: Nginx/HAProxy
- **Purpose**: Route traffic, SSL termination, rate limiting
- **Scaling**: Multiple instances behind DNS load balancer

#### 3.2 Commentator Service
- **Technology**: Spring Boot (Java)
- **Purpose**: Handle commentary updates from professionals
- **Features**: Authentication, validation, real-time publishing
- **Scaling**: 2-3 instances (low write volume)

#### 3.3 Reader Service
- **Technology**: Spring Boot (Java)
- **Purpose**: Serve commentary to end users
- **Features**: WebSocket connections, caching, real-time updates
- **Scaling**: Auto-scaling based on CPU/memory (50-100 instances)

#### 3.4 Cache Layer
- **Technology**: Redis Cluster
- **Purpose**: Ultra-fast reads, session management
- **Data**: Current match state, recent commentary, user sessions
- **Scaling**: 10-node cluster with replication

#### 3.5 Database
- **Technology**: MySQL with Master-Slave replication
- **Purpose**: Persistent storage, historical data
- **Features**: ACID compliance, backup/recovery
- **Scaling**: Read replicas for analytics

#### 3.6 Message Queue
- **Technology**: Apache Kafka
- **Purpose**: Decouple writes from cache updates
- **Features**: Guaranteed delivery, ordering, replay capability

## 4. Database Design

### 4.1 Schema Design

```sql
-- Matches table
CREATE TABLE matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    team1 VARCHAR(100) NOT NULL,
    team2 VARCHAR(100) NOT NULL,
    venue VARCHAR(200),
    match_date DATETIME,
    status ENUM('upcoming', 'live', 'completed'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_status_date (status, match_date)
);

-- Commentary table
CREATE TABLE commentary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    match_id BIGINT NOT NULL,
    over_number INT NOT NULL,
    ball_number INT NOT NULL,
    commentary_text TEXT NOT NULL,
    commentator_id VARCHAR(50),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(id),
    INDEX idx_match_over_ball (match_id, over_number, ball_number),
    INDEX idx_timestamp (timestamp)
);

-- Match state for quick access
CREATE TABLE match_state (
    match_id BIGINT PRIMARY KEY,
    current_over INT DEFAULT 0,
    current_ball INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(id)
);
```

### 4.2 Data Access Patterns
- **Writes**: Sequential inserts only (no updates/deletes during live match)
- **Reads**: Range queries by match_id, over, ball
- **Indexing**: Optimized for time-series access patterns

## 5. Caching Strategy

### 5.1 Cache Layers
1. **L1 Cache**: Application-level (Caffeine) - 1 second TTL
2. **L2 Cache**: Redis - Current match data, 10 minute TTL
3. **L3 Cache**: CDN - Static assets, long TTL

### 5.2 Cache Keys
```
match:{match_id}:current_state
match:{match_id}:commentary:{over}:{ball}
match:{match_id}:recent_commentary (last 10 balls)
user_session:{user_id}
```

### 5.3 Cache Invalidation
- **Write-through**: Updates go to DB first, then cache
- **Event-driven**: Kafka events trigger cache updates
- **TTL-based**: Automatic expiration for consistency

## 6. Real-time Communication

### 6.1 WebSocket Architecture
- **Connection Management**: Sticky sessions with Redis
- **Message Broadcasting**: Pub/Sub pattern
- **Fallback**: Server-Sent Events (SSE) for older browsers

### 6.2 Message Flow
```
Commentator Update → Kafka → Cache Update → WebSocket Broadcast → Clients
```

## 7. Scalability & Performance

### 7.1 Horizontal Scaling
- **Stateless Services**: All application servers are stateless
- **Database Sharding**: By match_id if needed (future)
- **Cache Partitioning**: Redis cluster with consistent hashing

### 7.2 Performance Optimizations
- **Connection Pooling**: Database and Redis connections
- **Async Processing**: Non-blocking I/O for WebSocket handling
- **Batch Operations**: Group cache updates
- **Compression**: Gzip for API responses

## 8. Reliability & Availability

### 8.1 High Availability
- **Multi-AZ Deployment**: Services across availability zones
- **Health Checks**: Automated failover
- **Circuit Breakers**: Prevent cascade failures
- **Graceful Degradation**: Serve cached data if DB is down

### 8.2 Data Durability
- **Database Replication**: Master-slave with automatic failover
- **Backup Strategy**: Daily full backups, hourly incrementals
- **Kafka Retention**: 7-day message retention for replay
- **Redis Persistence**: RDB + AOF for cache recovery

## 9. Monitoring & Observability

### 9.1 Metrics
- **Application**: Response time, error rate, throughput
- **Infrastructure**: CPU, memory, network, disk I/O
- **Business**: Active users, commentary frequency, engagement

### 9.2 Alerting
- **Critical**: Service down, database connection loss
- **Warning**: High latency, memory usage above 80%
- **Info**: Unusual traffic patterns, cache hit rate drops

## 10. Cost Optimization

### 10.1 Resource Efficiency
- **Auto-scaling**: Scale down during low traffic
- **Reserved Instances**: For predictable baseline load
- **Spot Instances**: For non-critical batch processing
- **CDN**: Reduce bandwidth costs

### 10.2 Cost Breakdown (Monthly)
- **Compute**: $5,000 (50 instances × $100)
- **Database**: $1,500 (MySQL cluster)
- **Cache**: $2,000 (Redis cluster)
- **Network**: $1,000 (CDN + bandwidth)
- **Total**: ~$9,500/month for 5M users

## 11. Security Considerations

### 11.1 Authentication & Authorization
- **Commentator Auth**: JWT tokens with role-based access
- **API Security**: Rate limiting, input validation
- **Network Security**: VPC, security groups, WAF

### 11.2 Data Protection
- **Encryption**: TLS in transit, AES-256 at rest
- **Access Control**: Principle of least privilege
- **Audit Logging**: All commentary updates logged

## 12. Deployment & DevOps

### 12.1 Infrastructure as Code
- **Terraform**: Infrastructure provisioning
- **Kubernetes**: Container orchestration
- **Helm Charts**: Application deployment

### 12.2 CI/CD Pipeline
- **Testing**: Unit, integration, load testing
- **Deployment**: Blue-green deployments
- **Rollback**: Automated rollback on failure

## 13. Future Enhancements

### 13.1 Advanced Features
- **AI-powered Insights**: Automated statistics generation
- **Multi-language Support**: Commentary in multiple languages
- **Video Integration**: Sync with live video streams
- **Mobile Apps**: Native iOS/Android applications

### 13.2 Technical Improvements
- **GraphQL**: More efficient data fetching
- **Event Sourcing**: Complete audit trail
- **Machine Learning**: Predictive caching
- **Edge Computing**: Reduce latency globally

## 14. Risk Assessment & Mitigation

### 14.1 Technical Risks
- **Database Bottleneck**: Mitigated by read replicas and caching
- **Cache Failure**: Fallback to database with circuit breakers
- **Network Partitions**: Multi-region deployment
- **DDoS Attacks**: CDN and WAF protection

### 14.2 Business Risks
- **Traffic Spikes**: Auto-scaling and load testing
- **Commentator Errors**: Validation and approval workflow
- **Compliance**: GDPR, data retention policies

This design provides a robust, scalable, and cost-effective solution for serving live cricket commentary to 5 million concurrent users while maintaining low latency and high availability.
