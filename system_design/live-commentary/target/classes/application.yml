spring:
  application:
    name: cricket-commentary-service
  
  profiles:
    active: dev
  
  # Database Configuration
  datasource:
    url: ***********************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
  
  # Cache Configuration
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=60s
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: application/json,text/html,text/xml,text/plain,text/css,application/javascript
  tomcat:
    max-connections: 10000
    threads:
      max: 200
      min-spare: 10

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Application Specific Configuration
app:
  websocket:
    max-connections: 10000
    heartbeat-interval: 30000
    message-buffer-size: 1000
  
  cache:
    match-state-ttl: 600 # 10 minutes
    commentary-ttl: 3600 # 1 hour
    recent-commentary-size: 10
  
  performance:
    async-pool-size: 50
    max-concurrent-requests: 1000

# Logging Configuration
logging:
  level:
    com.cricket.commentary: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

logging:
  level:
    com.cricket.commentary: DEBUG
    org.springframework.web: DEBUG

---
# Docker Profile
spring:
  config:
    activate:
      on-profile: docker
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:cricket_commentary}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC
    username: ${DB_USERNAME:cricket_user}
    password: ${DB_PASSWORD:cricket_pass}
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
  jpa:
    show-sql: false

app:
  websocket:
    max-connections: 50000
  performance:
    async-pool-size: 100
    max-concurrent-requests: 5000

logging:
  level:
    com.cricket.commentary: INFO
    org.springframework.web: WARN

---
# Load Test Profile
spring:
  config:
    activate:
      on-profile: loadtest
  datasource:
    hikari:
      maximum-pool-size: 100
      minimum-idle: 20

app:
  websocket:
    max-connections: 100000
  performance:
    async-pool-size: 200
    max-concurrent-requests: 10000
