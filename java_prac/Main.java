public class Main {
    public static void main(String[] args) throws InterruptedException {
        UnsafeQueue<String> queue = new UnsafeQueue<>();
        for (int i = 1; i <= 10; i++) {
            queue.add("Message-" + i);
        }

        DistributedLock lock = new DistributedLock();

        Thread c1 = new Thread(new Consumer(queue, lock, "Consumer-1"));
        Thread c2 = new Thread(new Consumer(queue, lock, "Consumer-2"));
        Thread c3 = new Thread(new Consumer(queue, lock, "Consumer-3"));

        c1.start();
        c2.start();
        c3.start();

        c1.join();
        c2.join();
        c3.join();

        System.out.println("All messages consumed.");
    }
}
