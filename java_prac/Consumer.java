public class Consumer implements Runnable {
    private final UnsafeQueue<String> queue;
    private final DistributedLock lock;
    private final String name;

    public Consumer(UnsafeQueue<String> queue, DistributedLock lock, String name) {
        this.queue = queue;
        this.lock = lock;
        this.name = name;
    }

    @Override
    public void run() {
        while (true) {
            if (lock.tryLock()) {
                try {
                    String item = queue.poll();
                    if (item == null) {
                        break;
                    }
                    System.out.println(name + " consumed: " + item);
                    // Simulate processing
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    lock.unlock();
                }
            } else {
                // Wait and retry
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
