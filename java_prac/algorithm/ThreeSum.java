package com.cricket.commentary.algorithm;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Solution for the 3Sum problem:
 * Given an integer array nums, return all the triplets [nums[i], nums[j], nums[k]] 
 * such that i != j, i != k, and j != k, and nums[i] + nums[j] + nums[k] == 0.
 * The solution set must not contain duplicate triplets.
 */
public class ThreeSum {
    
    /**
     * Finds all unique triplets in the array that sum to zero.
     * 
     * @param nums The input array
     * @return List of triplets that sum to zero
     */
    public List<List<Integer>> threeSum(int[] nums) {
        List<List<Integer>> result = new ArrayList<>();
        
        // Edge case: if array is null or has less than 3 elements
        if (nums == null || nums.length < 3) {
            return result;
        }
        
        // Sort the array to handle duplicates efficiently
        Arrays.sort(nums);
        
        // Iterate through the array
        for (int i = 0; i < nums.length - 2; i++) {
            // Skip duplicates for the first element
            if (i > 0 && nums[i] == nums[i - 1]) {
                continue;
            }
            
            // Use two pointers technique to find pairs that sum to -nums[i]
            int left = i + 1;
            int right = nums.length - 1;
            int target = -nums[i];
            
            while (left < right) {
                int sum = nums[left] + nums[right];
                
                if (sum == target) {
                    // Found a triplet
                    result.add(Arrays.asList(nums[i], nums[left], nums[right]));
                    
                    // Skip duplicates for the second element
                    while (left < right && nums[left] == nums[left + 1]) {
                        left++;
                    }
                    
                    // Skip duplicates for the third element
                    while (left < right && nums[right] == nums[right - 1]) {
                        right--;
                    }
                    
                    // Move both pointers
                    left++;
                    right--;
                } else if (sum < target) {
                    // Sum is too small, move left pointer to increase sum
                    left++;
                } else {
                    // Sum is too large, move right pointer to decrease sum
                    right--;
                }
            }
        }
        
        return result;
    }
    
    /**
     * Main method to test the solution with examples.
     */
    public static void main(String[] args) {
        ThreeSum solution = new ThreeSum();
        
        // Example 1
        int[] nums1 = {-1, 0, 1, 2, -1, -4};
        System.out.println("Example 1 Output: " + solution.threeSum(nums1));
        // Expected: [[-1, -1, 2], [-1, 0, 1]]
        
        // Example 2
        int[] nums2 = {0, 1, 1};
        System.out.println("Example 2 Output: " + solution.threeSum(nums2));
        // Expected: []
        
        // Example 3
        int[] nums3 = {0, 0, 0};
        System.out.println("Example 3 Output: " + solution.threeSum(nums3));
        // Expected: [[0, 0, 0]]
    }
}