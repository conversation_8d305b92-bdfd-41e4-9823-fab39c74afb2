import java.util.List;

/**
 * Interface for spatial indexing structures.
 */
public interface SpatialIndex<T> {
    
    /**
     * Adds an item to the spatial index.
     * 
     * @param item The item to add
     * @param point The geographic point associated with the item
     */
    void add(T item, GeoPoint point);
    
    /**
     * Removes an item from the spatial index.
     * 
     * @param item The item to remove
     * @return true if the item was removed, false otherwise
     */
    boolean remove(T item);
    
    /**
     * Updates the location of an item in the spatial index.
     * 
     * @param item The item to update
     * @param newPoint The new geographic point
     * @return true if the item was updated, false otherwise
     */
    boolean update(T item, GeoPoint newPoint);
    
    /**
     * Finds all items within a specified radius of a point.
     * 
     * @param center The center point
     * @param radiusKm The radius in kilometers
     * @return A list of items within the radius
     */
    List<T> findNearby(GeoPoint center, double radiusKm);
    
    /**
     * Gets the total number of items in the index.
     * 
     * @return The number of items
     */
    int size();
    
    /**
     * Clears all items from the index.
     */
    void clear();
}