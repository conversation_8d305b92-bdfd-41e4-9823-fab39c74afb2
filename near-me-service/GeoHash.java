import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for geohash operations.
 * Geohash is a public domain geocoding system that encodes geographic coordinates
 * into a short string of letters and digits.
 */
public class GeoHash {
    private static final String BASE32 = "0123456789bcdefghjkmnpqrstuvwxyz";
    
    // Precision levels (in bits)
    private static final int[] BITS_PER_CHAR = {5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60};
    
    // Approximate error in kilometers for different precision levels
    private static final double[] PRECISION_KM = {
        2500, 630, 78, 20, 2.4, 0.61, 0.076, 0.019, 0.0024, 0.00060, 0.000074, 0.000019
    };
    
    /**
     * Encodes a geographic point into a geohash string.
     * 
     * @param point The geographic point
     * @param precision The precision (length of the geohash string)
     * @return The geohash string
     */
    public static String encode(GeoPoint point, int precision) {
        if (precision < 1 || precision > 12) {
            throw new IllegalArgumentException("Precision must be between 1 and 12");
        }
        
        double lat = point.getLatitude();
        double lon = point.getLongitude();
        
        double latMin = -90.0;
        double latMax = 90.0;
        double lonMin = -180.0;
        double lonMax = 180.0;
        
        StringBuilder geohash = new StringBuilder();
        boolean isEven = true;
        int bit = 0;
        int ch = 0;
        
        while (geohash.length() < precision) {
            if (isEven) {
                double mid = (lonMin + lonMax) / 2;
                if (lon >= mid) {
                    ch |= (1 << (4 - bit));
                    lonMin = mid;
                } else {
                    lonMax = mid;
                }
            } else {
                double mid = (latMin + latMax) / 2;
                if (lat >= mid) {
                    ch |= (1 << (4 - bit));
                    latMin = mid;
                } else {
                    latMax = mid;
                }
            }
            
            isEven = !isEven;
            
            if (bit < 4) {
                bit++;
            } else {
                geohash.append(BASE32.charAt(ch));
                bit = 0;
                ch = 0;
            }
        }
        
        return geohash.toString();
    }
    
    /**
     * Decodes a geohash string into a geographic point.
     * 
     * @param geohash The geohash string
     * @return The geographic point
     */
    public static GeoPoint decode(String geohash) {
        double latMin = -90.0;
        double latMax = 90.0;
        double lonMin = -180.0;
        double lonMax = 180.0;
        boolean isEven = true;
        
        for (int i = 0; i < geohash.length(); i++) {
            char c = geohash.charAt(i);
            int cd = BASE32.indexOf(c);
            
            for (int j = 0; j < 5; j++) {
                int mask = 1 << (4 - j);
                if (isEven) {
                    if ((cd & mask) != 0) {
                        lonMin = (lonMin + lonMax) / 2;
                    } else {
                        lonMax = (lonMin + lonMax) / 2;
                    }
                } else {
                    if ((cd & mask) != 0) {
                        latMin = (latMin + latMax) / 2;
                    } else {
                        latMax = (latMin + latMax) / 2;
                    }
                }
                isEven = !isEven;
            }
        }
        
        double lat = (latMin + latMax) / 2;
        double lon = (lonMin + lonMax) / 2;
        
        return new GeoPoint(lat, lon);
    }
    
    /**
     * Gets the neighbors of a geohash.
     * 
     * @param geohash The geohash string
     * @return A list of neighboring geohashes
     */
    public static List<String> getNeighbors(String geohash) {
        List<String> neighbors = new ArrayList<>();
        
        GeoPoint center = decode(geohash);
        double lat = center.getLatitude();
        double lon = center.getLongitude();
        
        // Calculate the width and height of the geohash cell
        int precision = geohash.length();
        double latHeight = 180.0 / Math.pow(2, 5 * precision / 2);
        double lonWidth = 360.0 / Math.pow(2, 5 * precision / 2);
        
        // Calculate the coordinates of the 8 neighboring cells
        double[] lats = {lat, lat + latHeight, lat - latHeight};
        double[] lons = {lon, lon + lonWidth, lon - lonWidth};
        
        for (double neighborLat : lats) {
            for (double neighborLon : lons) {
                // Skip the center cell
                if (neighborLat == lat && neighborLon == lon) {
                    continue;
                }
                
                // Ensure coordinates are within valid range
                if (neighborLat < -90 || neighborLat > 90 || neighborLon < -180 || neighborLon > 180) {
                    continue;
                }
                
                GeoPoint neighborPoint = new GeoPoint(neighborLat, neighborLon);
                String neighborHash = encode(neighborPoint, precision);
                neighbors.add(neighborHash);
            }
        }
        
        return neighbors;
    }
    
    /**
     * Determines the appropriate geohash precision for a given radius in kilometers.
     * 
     * @param radiusKm The radius in kilometers
     * @return The recommended geohash precision
     */
    public static int getPrecisionForRadius(double radiusKm) {
        for (int i = 0; i < PRECISION_KM.length; i++) {
            if (PRECISION_KM[i] <= radiusKm) {
                return i + 1;
            }
        }
        return PRECISION_KM.length;
    }
}