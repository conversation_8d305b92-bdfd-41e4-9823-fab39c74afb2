import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Service for finding nearby people based on geographic location.
 */
public class NearbyPeopleService {
    
    private final SpatialIndex<User> spatialIndex;
    private final ReadWriteLock lock;
    
    /**
     * Creates a new NearbyPeopleService with a thread-safe GeohashIndex.
     */
    public NearbyPeopleService() {
        this.spatialIndex = new GeohashIndex<>(true);
        this.lock = new ReentrantReadWriteLock();
    }
    
    /**
     * Adds a user to the service.
     * 
     * @param user The user to add
     */
    public void addUser(User user) {
        if (user == null) {
            throw new IllegalArgumentException("User cannot be null");
        }
        
        lock.writeLock().lock();
        try {
            spatialIndex.add(user, user.getLocation());
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Updates a user's location.
     * 
     * @param user The user to update
     * @param newLocation The new location
     * @return true if the user was updated, false otherwise
     */
    public boolean updateUserLocation(User user, GeoPoint newLocation) {
        if (user == null || newLocation == null) {
            return false;
        }
        
        lock.writeLock().lock();
        try {
            // Update the user's location
            user.updateLocation(newLocation);
            
            // Update the spatial index
            return spatialIndex.update(user, newLocation);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Removes a user from the service.
     * 
     * @param user The user to remove
     * @return true if the user was removed, false otherwise
     */
    public boolean removeUser(User user) {
        if (user == null) {
            return false;
        }
        
        lock.writeLock().lock();
        try {
            return spatialIndex.remove(user);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Finds all users within a specified distance of a location.
     * 
     * @param center The center location
     * @param radiusKm The radius in kilometers
     * @return A list of users within the radius
     */
    public List<User> findNearbyUsers(GeoPoint center, double radiusKm) {
        if (center == null || radiusKm <= 0) {
            throw new IllegalArgumentException("Center cannot be null and radius must be positive");
        }
        
        lock.readLock().lock();
        try {
            return spatialIndex.findNearby(center, radiusKm);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Finds all users within a specified distance of a user.
     * 
     * @param user The user to find nearby users for
     * @param radiusKm The radius in kilometers
     * @return A list of users within the radius
     */
    public List<User> findNearbyUsers(User user, double radiusKm) {
        if (user == null || radiusKm <= 0) {
            throw new IllegalArgumentException("User cannot be null and radius must be positive");
        }
        
        return findNearbyUsers(user.getLocation(), radiusKm);
    }
    
    /**
     * Gets the total number of users in the service.
     * 
     * @return The number of users
     */
    public int getUserCount() {
        lock.readLock().lock();
        try {
            return spatialIndex.size();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Clears all users from the service.
     */
    public void clearAllUsers() {
        lock.writeLock().lock();
        try {
            spatialIndex.clear();
        } finally {
            lock.writeLock().unlock();
        }
    }
}