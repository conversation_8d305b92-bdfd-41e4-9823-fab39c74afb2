import java.util.List;
import java.util.UUID;

/**
 * Demo application for the NearbyPeopleService.
 */
public class NearbyPeopleDemo {
    
    public static void main(String[] args) {
        // Create the service
        NearbyPeopleService service = new NearbyPeopleService();
        
        // Create some users at different locations
        User alice = createUser("Alice", 37.7749, -122.4194); // San Francisco
        User bob = createUser("<PERSON>", 37.7833, -122.4167);     // San Francisco (nearby)
        User charlie = createUser("<PERSON>", 37.3382, -121.8863); // San Jose
        User dave = createUser("<PERSON>", 34.0522, -118.2437);   // Los Angeles
        User eve = createUser("Eve", 40.7128, -74.0060);      // New York
        
        // Add users to the service
        service.addUser(alice);
        service.addUser(bob);
        service.addUser(charlie);
        service.addUser(dave);
        service.addUser(eve);
        
        System.out.println("Total users: " + service.getUserCount());
        
        // Find users near San Francisco (within 10 km)
        GeoPoint sanFrancisco = new GeoPoint(37.7749, -122.4194);
        List<User> nearSF = service.findNearbyUsers(sanFrancisco, 10.0);
        
        System.out.println("\nUsers within 10 km of San Francisco:");
        printUsers(nearSF);
        
        // Find users near San Francisco (within 100 km)
        nearSF = service.findNearbyUsers(sanFrancisco, 100.0);
        
        System.out.println("\nUsers within 100 km of San Francisco:");
        printUsers(nearSF);
        
        // Update Bob's location to New York
        GeoPoint newYork = new GeoPoint(40.7128, -74.0060);
        System.out.println("\nUpdating " + bob.getName() + "'s location to New York...");
        service.updateUserLocation(bob, newYork);
        
        // Find users near New York (within 10 km)
        List<User> nearNY = service.findNearbyUsers(newYork, 10.0);
        
        System.out.println("\nUsers within 10 km of New York:");
        printUsers(nearNY);
        
        // Find users near Alice
        List<User> nearAlice = service.findNearbyUsers(alice, 100.0);
        
        System.out.println("\nUsers within 100 km of " + alice.getName() + ":");
        printUsers(nearAlice);
        
        // Remove a user
        System.out.println("\nRemoving " + charlie.getName() + "...");
        service.removeUser(charlie);
        
        System.out.println("Total users: " + service.getUserCount());
        
        // Find users near San Francisco again
        nearSF = service.findNearbyUsers(sanFrancisco, 100.0);
        
        System.out.println("\nUsers within 100 km of San Francisco (after removing " + charlie.getName() + "):");
        printUsers(nearSF);
        
        // Performance test
        performanceTest(1000);
    }
    
    private static User createUser(String name, double latitude, double longitude) {
        String userId = UUID.randomUUID().toString();
        GeoPoint location = new GeoPoint(latitude, longitude);
        return new User(userId, name, location);
    }
    
    private static void printUsers(List<User> users) {
        if (users.isEmpty()) {
            System.out.println("  No users found");
            return;
        }
        
        for (User user : users) {
            System.out.println("  " + user);
        }
    }
    
    private static void performanceTest(int numUsers) {
        System.out.println("\n=== Performance Test ===");
        System.out.println("Creating a service with " + numUsers + " random users...");
        
        NearbyPeopleService service = new NearbyPeopleService();
        
        // Create random users
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < numUsers; i++) {
            // Random location within the continental US
            double lat = 30.0 + Math.random() * 20.0; // ~30-50 degrees N
            double lon = -120.0 + Math.random() * 40.0; // ~120-80 degrees W
            
            User user = createUser("User" + i, lat, lon);
            service.addUser(user);
        }
        
        long createTime = System.currentTimeMillis() - startTime;
        System.out.println("Time to create and add " + numUsers + " users: " + createTime + " ms");
        
        // Test query performance
        GeoPoint queryPoint = new GeoPoint(37.7749, -122.4194); // San Francisco
        double queryRadius = 100.0; // 100 km
        
        startTime = System.currentTimeMillis();
        List<User> nearbyUsers = service.findNearbyUsers(queryPoint, queryRadius);
        long queryTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Time to find users within " + queryRadius + " km: " + queryTime + " ms");
        System.out.println("Found " + nearbyUsers.size() + " users");
        
        // Test update performance
        User randomUser = nearbyUsers.isEmpty() ? 
                createUser("RandomUser", 37.7749, -122.4194) : 
                nearbyUsers.get(0);
        
        if (nearbyUsers.isEmpty()) {
            service.addUser(randomUser);
        }
        
        GeoPoint newLocation = new GeoPoint(40.7128, -74.0060); // New York
        
        startTime = System.currentTimeMillis();
        service.updateUserLocation(randomUser, newLocation);
        long updateTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Time to update a user's location: " + updateTime + " ms");
    }
}