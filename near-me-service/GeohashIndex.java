import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Implementation of SpatialIndex using geohashes for efficient spatial indexing.
 */
public class GeohashIndex<T> implements SpatialIndex<T> {
    
    // Default precision for geohashes
    private static final int DEFAULT_PRECISION = 7; // ~76m precision
    
    // Maps items to their geohashes
    private final Map<T, String> itemToGeohash;
    
    // Maps geohashes to sets of items (reverse index)
    private final Map<String, Set<T>> geohashToItems;
    
    // Maps items to their geographic points
    private final Map<T, GeoPoint> itemToPoint;
    
    /**
     * Creates a new GeohashIndex with default thread-safe implementation.
     */
    public GeohashIndex() {
        this.itemToGeohash = new ConcurrentHashMap<>();
        this.geohashToItems = new ConcurrentHashMap<>();
        this.itemToPoint = new ConcurrentHashMap<>();
    }
    
    /**
     * Creates a new GeohashIndex with specified thread-safety.
     * 
     * @param concurrent If true, uses thread-safe collections
     */
    public GeohashIndex(boolean concurrent) {
        if (concurrent) {
            this.itemToGeohash = new ConcurrentHashMap<>();
            this.geohashToItems = new ConcurrentHashMap<>();
            this.itemToPoint = new ConcurrentHashMap<>();
        } else {
            this.itemToGeohash = new HashMap<>();
            this.geohashToItems = new HashMap<>();
            this.itemToPoint = new HashMap<>();
        }
    }
    
    @Override
    public void add(T item, GeoPoint point) {
        if (item == null || point == null) {
            throw new IllegalArgumentException("Item and point cannot be null");
        }
        
        String geohash = GeoHash.encode(point, DEFAULT_PRECISION);
        
        // Store the item's geohash
        itemToGeohash.put(item, geohash);
        
        // Store the item's point
        itemToPoint.put(item, point);
        
        // Add the item to the reverse index
        geohashToItems.computeIfAbsent(geohash, k -> Collections.newSetFromMap(new ConcurrentHashMap<>()))
                      .add(item);
    }
    
    @Override
    public boolean remove(T item) {
        if (item == null) {
            return false;
        }
        
        String geohash = itemToGeohash.remove(item);
        if (geohash == null) {
            return false;
        }
        
        // Remove from the reverse index
        Set<T> items = geohashToItems.get(geohash);
        if (items != null) {
            items.remove(item);
            if (items.isEmpty()) {
                geohashToItems.remove(geohash);
            }
        }
        
        // Remove the point
        itemToPoint.remove(item);
        
        return true;
    }
    
    @Override
    public boolean update(T item, GeoPoint newPoint) {
        if (item == null || newPoint == null) {
            return false;
        }
        
        // Check if the item exists
        if (!itemToGeohash.containsKey(item)) {
            return false;
        }
        
        // Remove the item from the old location
        remove(item);
        
        // Add the item at the new location
        add(item, newPoint);
        
        return true;
    }
    
    @Override
    public List<T> findNearby(GeoPoint center, double radiusKm) {
        if (center == null || radiusKm <= 0) {
            return Collections.emptyList();
        }
        
        // Determine the appropriate precision for the given radius
        int precision = GeoHash.getPrecisionForRadius(radiusKm);
        
        // Get the geohash of the center point at the determined precision
        String centerGeohash = GeoHash.encode(center, precision);
        
        // Get all neighboring geohashes
        List<String> neighborGeohashes = new ArrayList<>();
        neighborGeohashes.add(centerGeohash);
        neighborGeohashes.addAll(GeoHash.getNeighbors(centerGeohash));
        
        // Collect all items in the neighboring geohashes
        Set<T> candidateItems = new HashSet<>();
        for (String geohash : neighborGeohashes) {
            // For each neighboring geohash, find all items in geohashes that start with it
            for (Map.Entry<String, Set<T>> entry : geohashToItems.entrySet()) {
                if (entry.getKey().startsWith(geohash)) {
                    candidateItems.addAll(entry.getValue());
                }
            }
        }
        
        // Filter items by actual distance
        return candidateItems.stream()
                .filter(item -> {
                    GeoPoint itemPoint = itemToPoint.get(item);
                    return itemPoint != null && itemPoint.distanceTo(center) <= radiusKm;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public int size() {
        return itemToGeohash.size();
    }
    
    @Override
    public void clear() {
        itemToGeohash.clear();
        geohashToItems.clear();
        itemToPoint.clear();
    }
    
    /**
     * Gets the geohash for a specific item.
     * 
     * @param item The item
     * @return The geohash or null if the item is not in the index
     */
    public String getGeohash(T item) {
        return itemToGeohash.get(item);
    }
    
    /**
     * Gets all items in a specific geohash.
     * 
     * @param geohash The geohash
     * @return A set of items in the geohash
     */
    public Set<T> getItemsInGeohash(String geohash) {
        Set<T> items = geohashToItems.get(geohash);
        return items != null ? Collections.unmodifiableSet(items) : Collections.emptySet();
    }
}