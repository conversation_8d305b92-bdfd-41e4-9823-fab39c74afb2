/**
 * Represents a user in the system with location information.
 */
public class User {
    private final String userId;
    private final String name;
    private GeoPoint location;
    private long lastUpdateTimestamp;
    
    public User(String userId, String name, GeoPoint location) {
        this.userId = userId;
        this.name = name;
        this.location = location;
        this.lastUpdateTimestamp = System.currentTimeMillis();
    }
    
    public String getUserId() {
        return userId;
    }
    
    public String getName() {
        return name;
    }
    
    public GeoPoint getLocation() {
        return location;
    }
    
    public long getLastUpdateTimestamp() {
        return lastUpdateTimestamp;
    }
    
    /**
     * Updates the user's location.
     * 
     * @param newLocation The new location
     */
    public void updateLocation(GeoPoint newLocation) {
        this.location = newLocation;
        this.lastUpdateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * Calculates the distance to another user.
     * 
     * @param other The other user
     * @return The distance in kilometers
     */
    public double distanceTo(User other) {
        return this.location.distanceTo(other.location);
    }
    
    @Override
    public String toString() {
        return String.format("User{id=%s, name=%s, location=%s}", 
                userId, name, location);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        User other = (User) obj;
        return userId.equals(other.userId);
    }
    
    @Override
    public int hashCode() {
        return userId.hashCode();
    }
}