# Near Me Service

A location-based service that efficiently finds people within a specified distance.

## Overview

This project implements a scalable and efficient system for finding nearby people based on geographic location. It uses geohash-based spatial indexing to optimize proximity queries and supports high concurrency with thread-safe operations.

## Components

- **GeoPoint**: Represents geographic coordinates with latitude and longitude
- **User**: Represents a user with ID, name, and location information
- **GeoHash**: Utility class for geohash operations
- **SpatialIndex**: Interface for spatial indexing structures
- **GeohashIndex**: Implementation of SpatialIndex using geohashes
- **NearbyPeopleService**: Main service for finding nearby users
- **NearbyPeopleDemo**: Demo application showcasing the implementation

## Features

- Efficient spatial indexing using geohashes
- Accurate distance calculation using the Haversine formula
- Thread-safe operations with concurrent collections and locks
- Scalable architecture that can handle millions of users
- Comprehensive design document with system architecture details

## How to Run

To run the demo, you need to have Java installed on your system. Once Java is installed:

1. Compile all the Java files:
   ```
   javac *.java
   ```

2. Run the demo:
   ```
   java NearbyPeopleDemo
   ```

## Design Considerations

The implementation follows the design outlined in the `DesignDocument.md` file, which includes:

- System architecture
- Data model
- Spatial indexing approaches
- Scaling strategy
- High availability design
- Data durability
- Cost optimization
- Capacity planning
- Algorithm details
- Trade-offs and limitations

## Performance

The system is designed to handle:
- 1 million users
- ~10 location updates per user per day
- ~100 queries per second at peak load

The geohash-based spatial indexing provides efficient query performance with O(log n) complexity for finding nearby users.